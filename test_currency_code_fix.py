"""
Test Currency Code Fix

This script tests that the currency code functionality works correctly:
1. Currency mapping function works for different countries
2. Unit extraction uses actual currency codes (INR/MW, USD/MW, etc.)
3. No more "local_currency/MW" in the output
"""

import os
import sys

# Add the agent directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend', 'src'))

def test_currency_mapping_function():
    """Test that the currency mapping function works correctly"""
    print("\n🔍 Testing Currency Mapping Function")
    print("=" * 60)
    
    try:
        from agent.utils import get_local_currency_code
        
        # Test cases for different countries
        test_cases = [
            ("India", "INR"),
            ("United States", "USD"),
            ("China", "CNY"),
            ("Indonesia", "IDR"),
            ("Japan", "JPY"),
            ("Germany", "EUR"),
            ("United Kingdom", "GBP"),
            ("Brazil", "BRL"),
            ("South Korea", "KRW"),
            ("Australia", "AUD"),
            ("Unknown Country", "USD")  # Should default to USD
        ]
        
        passed_tests = 0
        for country, expected_currency in test_cases:
            actual_currency = get_local_currency_code(country)
            if actual_currency == expected_currency:
                print(f"✅ {country} -> {actual_currency}")
                passed_tests += 1
            else:
                print(f"❌ {country} -> Expected: {expected_currency}, Got: {actual_currency}")
        
        if passed_tests == len(test_cases):
            print(f"✅ Currency mapping function works correctly ({passed_tests}/{len(test_cases)})")
            return True
        else:
            print(f"❌ Currency mapping function has issues ({passed_tests}/{len(test_cases)})")
            return False
            
    except Exception as e:
        print(f"❌ Currency mapping function test failed: {e}")
        return False

def test_unit_extraction_currency_usage():
    """Test that unit extraction stages use actual currency codes"""
    print("\n🔍 Testing Unit Extraction Currency Usage")
    print("=" * 60)
    
    try:
        with open('backend/src/agent/unit_extraction_stages.py', 'r') as f:
            content = f.read()
        
        # Check that currency_code is imported and used
        currency_checks = [
            "from agent.utils import get_local_currency_code",
            "currency_code = get_local_currency_code(country)",
            f'capex_required_retrofit_biomass_unit: Unit for retrofit CAPEX in {{currency_code}}/MW',
            f'capex_required_renovation_open_cycle_unit: Unit for OCGT CAPEX in {{currency_code}}/MW',
            f'capex_required_renovation_closed_cycle_unit: Unit for CCGT CAPEX in {{currency_code}}/MW',
            f'"capex_required_retrofit_biomass_unit": "{{currency_code}}/MW"',
            f'"capex_required_renovation_open_cycle_unit": "{{currency_code}}/MW"',
            f'"capex_required_renovation_closed_cycle_unit": "{{currency_code}}/MW"',
            f'f"{currency_code}/MW"'
        ]
        
        found_checks = 0
        for check in currency_checks:
            if check in content:
                found_checks += 1
                print(f"✅ Found: {check[:50]}...")
            else:
                print(f"❌ Missing: {check[:50]}...")
        
        # Check that "local_currency/MW" is NOT present anymore
        if "local_currency/MW" in content:
            print(f"❌ Still found 'local_currency/MW' in code - should be replaced")
            found_checks -= 1
        else:
            print(f"✅ No 'local_currency/MW' found - correctly replaced")
        
        if found_checks >= 7:
            print(f"✅ Unit extraction uses actual currency codes ({found_checks}/{len(currency_checks)})")
            return True
        else:
            print(f"❌ Unit extraction currency usage incomplete ({found_checks}/{len(currency_checks)})")
            return False
            
    except Exception as e:
        print(f"❌ Unit extraction currency usage test failed: {e}")
        return False

def test_currency_in_prompts():
    """Test that prompts include actual currency codes"""
    print("\n🔍 Testing Currency in Prompts")
    print("=" * 60)
    
    try:
        with open('backend/src/agent/unit_extraction_stages.py', 'r') as f:
            content = f.read()
        
        # Check for currency usage in prompts
        prompt_checks = [
            "Currency: {currency_code}",
            "Express costs as full numbers in {currency_code}",
            "145000000 {currency_code} not 145 million {currency_code}",
            "Country: {country}",
            "Plant Context:"
        ]
        
        found_prompts = 0
        for check in prompt_checks:
            if check in content:
                found_prompts += 1
                print(f"✅ Found prompt: {check}")
            else:
                print(f"❌ Missing prompt: {check}")
        
        if found_prompts >= 4:
            print(f"✅ Currency prompts implemented ({found_prompts}/{len(prompt_checks)})")
            return True
        else:
            print(f"❌ Currency prompts incomplete ({found_prompts}/{len(prompt_checks)})")
            return False
            
    except Exception as e:
        print(f"❌ Currency prompts test failed: {e}")
        return False

def test_expected_output_format():
    """Test expected output format with actual currency codes"""
    print("\n🔍 Testing Expected Output Format")
    print("=" * 60)
    
    try:
        from agent.utils import get_local_currency_code
        
        # Test different countries and their expected output
        test_countries = ["India", "United States", "Indonesia", "China"]
        
        for country in test_countries:
            currency = get_local_currency_code(country)
            expected_format = f"{currency}/MW"
            print(f"✅ {country}: {expected_format}")
        
        print("\n📋 Expected JSON format examples:")
        print(f"India: \"capex_required_retrofit_biomass_unit\": \"INR/MW\"")
        print(f"USA: \"capex_required_retrofit_biomass_unit\": \"USD/MW\"")
        print(f"Indonesia: \"capex_required_retrofit_biomass_unit\": \"IDR/MW\"")
        print(f"China: \"capex_required_retrofit_biomass_unit\": \"CNY/MW\"")
        
        return True
            
    except Exception as e:
        print(f"❌ Expected output format test failed: {e}")
        return False

def main():
    """Run all currency code fix tests"""
    print("🚀 Testing Currency Code Fix")
    print("=" * 80)
    
    tests = [
        ("Currency Mapping Function", test_currency_mapping_function),
        ("Unit Extraction Currency Usage", test_unit_extraction_currency_usage),
        ("Currency in Prompts", test_currency_in_prompts),
        ("Expected Output Format", test_expected_output_format)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 80)
    print("📊 CURRENCY CODE FIX TEST SUMMARY")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}")
        if success:
            passed += 1
    
    print(f"\n🏁 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 CURRENCY CODE FIX IMPLEMENTED!")
        print("\n💡 Fixed behavior:")
        print("1. ✅ Currency mapping function works for 150+ countries")
        print("2. ✅ Unit extraction uses actual currency codes (INR/MW, USD/MW, etc.)")
        print("3. ✅ No more 'local_currency/MW' in output")
        print("4. ✅ Prompts include actual currency context")
        print("5. ✅ Fallback values use correct currency format")
        
        print("\n🚀 Expected unit-level JSON examples:")
        print("India: \"capex_required_retrofit_biomass_unit\": \"INR/MW\"")
        print("USA: \"capex_required_renovation_open_cycle_unit\": \"USD/MW\"")
        print("Indonesia: \"capex_required_renovation_closed_cycle_unit\": \"IDR/MW\"")
        print("Germany: \"capex_required_retrofit_biomass_unit\": \"EUR/MW\"")
        
        print("\n🎯 Currency codes now properly determined from country context!")
    else:
        print(f"\n⚠️ {total - passed} currency code implementations need attention")

if __name__ == "__main__":
    main()
