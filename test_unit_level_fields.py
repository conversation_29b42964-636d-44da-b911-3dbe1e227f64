"""
Test Unit Level Fields Implementation

This script tests that all the specific unit-level fields you requested are properly implemented:
1. annual_operational_hours (default: 8760)
2. blending_percentage (default: 0.15)
3. emission_factor_coking_coal (with description)
4. emission_factor_gas (default: 2.69)
5. emission_factor_of_gas_unit (default: kg CO2e/kg)
6. emission_factor_unit (default: kgCO_2/kWH)
7. fgds_status (with description)
8. heat_rate (with description)
9. heat_rate_unit (default: Kcal/kWh)
10. capex_required_retrofit_biomass (with description)
11. capex_required_retrofit_biomass_unit (default: local_currency/MW)
12. capex_required_renovation_open_cycle (with description)
13. capex_required_renovation_open_cycle_unit (default: local_currency/MW)
14. capex_required_renovation_closed_cycle (with description)
15. capex_required_renovation_closed_cycle_unit (default: local_currency/MW)
"""

import os
import sys

# Add the agent directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend', 'src'))

def test_unit_extraction_stages():
    """Test that unit extraction stages include all required fields"""
    print("\n🔍 Testing Unit Extraction Stages")
    print("=" * 60)
    
    try:
        with open('backend/src/agent/unit_extraction_stages.py', 'r') as f:
            content = f.read()
        
        # Check for all the specific fields in the extraction stages
        required_fields = [
            "annual_operational_hours",
            "blending_percentage", 
            "emission_factor_coking_coal",
            "emission_factor_gas",
            "emission_factor_of_gas_unit",
            "emission_factor_unit",
            "fgds_status",
            "heat_rate",
            "heat_rate_unit",
            "capex_required_retrofit_biomass",
            "capex_required_retrofit_biomass_unit",
            "capex_required_renovation_open_cycle",
            "capex_required_renovation_open_cycle_unit",
            "capex_required_renovation_closed_cycle",
            "capex_required_renovation_closed_cycle_unit"
        ]
        
        found_fields = 0
        for field in required_fields:
            if field in content:
                found_fields += 1
                print(f"✅ Found field: {field}")
            else:
                print(f"❌ Missing field: {field}")
        
        if found_fields == len(required_fields):
            print(f"✅ All {len(required_fields)} unit-level fields implemented")
            return True
        else:
            print(f"❌ Only {found_fields}/{len(required_fields)} unit-level fields found")
            return False
            
    except Exception as e:
        print(f"❌ Unit extraction stages test failed: {e}")
        return False

def test_specific_field_descriptions():
    """Test that specific field descriptions and prompts are implemented"""
    print("\n🔍 Testing Specific Field Descriptions")
    print("=" * 60)
    
    try:
        with open('backend/src/agent/unit_extraction_stages.py', 'r') as f:
            content = f.read()
        
        # Check for specific descriptions and prompts
        description_checks = [
            "primary type of coal (bituminous, sub-bituminous, lignite, anthracite)",
            "emission factor for that coal type in kg CO₂e/kg",
            "FGDS (Flue Gas Desulfurization System) status",
            "heat energy (in Kcal) needed to generate one kWh",
            "biomass co-firing retrofit using Palm Kernel Shells (PKS)",
            "Open Cycle Gas Turbine (OCGT) conversion",
            "Combined Cycle Gas Turbine (CCGT) conversion",
            "Express costs as full numbers in local currency"
        ]
        
        found_descriptions = 0
        for desc in description_checks:
            if desc in content:
                found_descriptions += 1
                print(f"✅ Found description: {desc[:50]}...")
            else:
                print(f"❌ Missing description: {desc[:50]}...")
        
        if found_descriptions >= 6:
            print(f"✅ Field descriptions implemented ({found_descriptions}/{len(description_checks)})")
            return True
        else:
            print(f"❌ Field descriptions incomplete ({found_descriptions}/{len(description_checks)})")
            return False
            
    except Exception as e:
        print(f"❌ Field descriptions test failed: {e}")
        return False

def test_default_values():
    """Test that default values are properly set"""
    print("\n🔍 Testing Default Values")
    print("=" * 60)
    
    try:
        with open('backend/src/agent/unit_extraction_stages.py', 'r') as f:
            content = f.read()
        
        # Check for specific default values
        default_checks = [
            '"annual_operational_hours": "8760"',
            '"blending_percentage": "0.15"',
            '"emission_factor_gas": "2.69"',
            '"emission_factor_of_gas_unit": "kg CO2e/kg"',
            '"emission_factor_unit": "kgCO_2/kWH"',
            '"heat_rate_unit": "Kcal/kWh"',
            '"capex_required_retrofit_biomass_unit": "local_currency/MW"',
            '"capex_required_renovation_open_cycle_unit": "local_currency/MW"',
            '"capex_required_renovation_closed_cycle_unit": "local_currency/MW"'
        ]
        
        found_defaults = 0
        for default in default_checks:
            if default in content:
                found_defaults += 1
                print(f"✅ Found default: {default}")
            else:
                print(f"❌ Missing default: {default}")
        
        if found_defaults >= 7:
            print(f"✅ Default values implemented ({found_defaults}/{len(default_checks)})")
            return True
        else:
            print(f"❌ Default values incomplete ({found_defaults}/{len(default_checks)})")
            return False
            
    except Exception as e:
        print(f"❌ Default values test failed: {e}")
        return False

def test_unit_research_prompts():
    """Test that unit research prompts include the new fields"""
    print("\n🔍 Testing Unit Research Prompts")
    print("=" * 60)
    
    try:
        with open('backend/src/agent/graph.py', 'r') as f:
            content = f.read()
        
        # Check for enhanced research prompts
        prompt_checks = [
            "CRITICAL UNIT-LEVEL FIELD RESEARCH",
            "Annual operational hours",
            "Heat rate in Kcal/kWh",
            "Blending percentage for biomass co-firing",
            "Primary coal type used",
            "FGDS (Flue Gas Desulfurization System) status",
            "CAPEX for biomass co-firing retrofit",
            "CAPEX for Open Cycle Gas Turbine (OCGT)",
            "CAPEX for Combined Cycle Gas Turbine (CCGT)"
        ]
        
        found_prompts = 0
        for prompt in prompt_checks:
            if prompt in content:
                found_prompts += 1
                print(f"✅ Found prompt: {prompt}")
            else:
                print(f"❌ Missing prompt: {prompt}")
        
        if found_prompts >= 7:
            print(f"✅ Unit research prompts enhanced ({found_prompts}/{len(prompt_checks)})")
            return True
        else:
            print(f"❌ Unit research prompts incomplete ({found_prompts}/{len(prompt_checks)})")
            return False
            
    except Exception as e:
        print(f"❌ Unit research prompts test failed: {e}")
        return False

def main():
    """Run all unit-level field tests"""
    print("🚀 Testing Unit Level Fields Implementation")
    print("=" * 80)
    
    tests = [
        ("Unit Extraction Stages", test_unit_extraction_stages),
        ("Specific Field Descriptions", test_specific_field_descriptions),
        ("Default Values", test_default_values),
        ("Unit Research Prompts", test_unit_research_prompts)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 80)
    print("📊 UNIT LEVEL FIELDS TEST SUMMARY")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}")
        if success:
            passed += 1
    
    print(f"\n🏁 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 ALL UNIT-LEVEL FIELDS IMPLEMENTED!")
        print("\n💡 Implemented fields:")
        print("1. ✅ annual_operational_hours: 8760 (default)")
        print("2. ✅ blending_percentage: 0.15 (default)")
        print("3. ✅ emission_factor_coking_coal: With coal type research")
        print("4. ✅ emission_factor_gas: 2.69 (default)")
        print("5. ✅ emission_factor_of_gas_unit: kg CO2e/kg (default)")
        print("6. ✅ emission_factor_unit: kgCO_2/kWH (default)")
        print("7. ✅ fgds_status: With FGDS system research")
        print("8. ✅ heat_rate: With Kcal/kWh research")
        print("9. ✅ heat_rate_unit: Kcal/kWh (default)")
        print("10. ✅ capex_required_retrofit_biomass: PKS retrofit research")
        print("11. ✅ capex_required_retrofit_biomass_unit: local_currency/MW")
        print("12. ✅ capex_required_renovation_open_cycle: OCGT research")
        print("13. ✅ capex_required_renovation_open_cycle_unit: local_currency/MW")
        print("14. ✅ capex_required_renovation_closed_cycle: CCGT research")
        print("15. ✅ capex_required_renovation_closed_cycle_unit: local_currency/MW")
        
        print("\n🚀 Expected unit-level JSON:")
        print("""
{
  "unit_number": 1,
  "annual_operational_hours": "8760",
  "blending_percentage": "0.15",
  "emission_factor_coking_coal": "Research-based value with source",
  "emission_factor_gas": "2.69",
  "emission_factor_of_gas_unit": "kg CO2e/kg",
  "emission_factor_unit": "kgCO_2/kWH",
  "fgds_status": "Fully installed and operational",
  "heat_rate": "Research-based value",
  "heat_rate_unit": "Kcal/kWh",
  "capex_required_retrofit_biomass": "Country-specific value",
  "capex_required_retrofit_biomass_unit": "local_currency/MW",
  "capex_required_renovation_open_cycle": "Country-specific value",
  "capex_required_renovation_open_cycle_unit": "local_currency/MW",
  "capex_required_renovation_closed_cycle": "Country-specific value",
  "capex_required_renovation_closed_cycle_unit": "local_currency/MW"
}""")
        
        print("\n🎯 All unit-level fields ready for extraction!")
    else:
        print(f"\n⚠️ {total - passed} unit-level field implementations need attention")

if __name__ == "__main__":
    main()
