#!/usr/bin/env python3
"""
Test script for SQS Financial Pipeline Integration

This script tests the SQS service and financial pipeline trigger functionality
to ensure messages are properly sent to the financial pipeline queue.
"""

import os
import sys
from dotenv import load_dotenv

# Add the src directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# Load environment variables
load_dotenv()

def test_sqs_connection():
    """Test SQS connection and queue accessibility"""
    print("🔍 Testing SQS Connection...")
    
    try:
        from agent.sqs_service import get_sqs_service
        
        sqs_service = get_sqs_service()
        success = sqs_service.test_connection("test_connection")
        
        if success:
            print("✅ SQS connection test PASSED")
            return True
        else:
            print("❌ SQS connection test FAILED")
            return False
            
    except Exception as e:
        print(f"❌ SQS connection test ERROR: {str(e)}")
        return False

def test_message_formatting():
    """Test message payload creation and formatting"""
    print("\n🔍 Testing Message Formatting...")
    
    try:
        from agent.sqs_service import SQSService
        
        sqs_service = SQSService()
        
        # Test message payload creation
        payload = sqs_service.create_message_payload(
            org_name="Test Organization Ltd",
            plant_name="Test Power Plant",
            country="India",
            uid="test-uid-12345",
            session_id="test_session"
        )
        
        print("✅ Message payload created successfully:")
        print(f"   - Message Type: {payload['message_type']}")
        print(f"   - Organization: {payload['data']['org_name']}")
        print(f"   - Plant: {payload['data']['plant_name']}")
        print(f"   - Country: {payload['data']['country']}")
        print(f"   - UID: {payload['data']['uid']}")
        
        # Test MessageGroupId generation
        group_id = sqs_service.generate_message_group_id("Test Power Plant")
        print(f"   - MessageGroupId: {group_id}")
        
        # Test MessageDeduplicationId generation
        dedup_id = sqs_service.generate_deduplication_id(payload)
        print(f"   - DeduplicationId: {dedup_id[:16]}...")
        
        print("✅ Message formatting test PASSED")
        return True
        
    except Exception as e:
        print(f"❌ Message formatting test ERROR: {str(e)}")
        return False

def test_sqs_message_sending():
    """Test actual SQS message sending"""
    print("\n🔍 Testing SQS Message Sending...")
    
    try:
        from agent.sqs_service import send_financial_pipeline_trigger
        
        # Send test message
        result = send_financial_pipeline_trigger(
            org_name="Test Organization Ltd",
            plant_name="Test Power Plant",
            country="India",
            uid="test-uid-12345",
            session_id="test_send"
        )
        
        if result["success"]:
            print("✅ SQS message sending test PASSED")
            print(f"   - Message ID: {result['message_id']}")
            print(f"   - Sequence Number: {result.get('sequence_number', 'N/A')}")
            print(f"   - Group ID: {result.get('message_group_id', 'N/A')}")
            return True
        else:
            print("❌ SQS message sending test FAILED")
            print(f"   - Error: {result.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ SQS message sending test ERROR: {str(e)}")
        return False

def test_financial_pipeline_node():
    """Test the financial pipeline trigger node"""
    print("\n🔍 Testing Financial Pipeline Node...")
    
    try:
        from agent.registry_nodes import trigger_financial_pipeline_node
        from agent.state import OverallState
        
        # Create test state
        test_state = {
            "session_id": "test_node",
            "org_name": "Test Organization Ltd",
            "plant_name": "Test Power Plant",
            "org_uid": "test-uid-12345",
            "messages": [
                type('Message', (), {
                    'content': 'Test Power Plant in India'
                })()
            ]
        }
        
        # Call the node
        result = trigger_financial_pipeline_node(test_state)
        
        if result.get("financial_pipeline_triggered"):
            print("✅ Financial pipeline node test PASSED")
            print(f"   - Triggered: {result['financial_pipeline_triggered']}")
            print(f"   - Message ID: {result.get('financial_trigger_message_id', 'N/A')}")
            print(f"   - Error: {result.get('financial_trigger_error', 'None')}")
            return True
        else:
            print("❌ Financial pipeline node test FAILED")
            print(f"   - Error: {result.get('financial_trigger_error', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ Financial pipeline node test ERROR: {str(e)}")
        return False

def main():
    """Run all SQS integration tests"""
    print("🚀 Starting SQS Financial Pipeline Integration Tests")
    print("=" * 60)
    
    # Check environment variables
    required_env_vars = [
        "AWS_ACCESS_KEY_ID",
        "AWS_SECRET_ACCESS_KEY", 
        "AWS_REGION",
        "SQS_QUEUE_URL"
    ]
    
    missing_vars = [var for var in required_env_vars if not os.getenv(var)]
    if missing_vars:
        print(f"❌ Missing environment variables: {', '.join(missing_vars)}")
        print("Please check your .env file configuration")
        return False
    
    print("✅ Environment variables configured")
    
    # Run tests
    tests = [
        test_sqs_connection,
        test_message_formatting,
        test_sqs_message_sending,
        test_financial_pipeline_node
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 60)
    print(f"🏁 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests PASSED! SQS integration is working correctly.")
        return True
    else:
        print("⚠️ Some tests FAILED. Please check the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)