"""
Completion Handler Module

This module handles the complete flow of receiving financial pipeline completion
messages and sending final completion messages to the backend team.
"""

import time
import json
from typing import Dict, Any, List, Optional
from datetime import datetime, timezone

from .sqs_service import get_sqs_service
from .backend_completion_service import send_backend_completion_message

class CompletionHandler:
    """Handles completion message flow between financial pipeline and backend team"""
    
    def __init__(self):
        """Initialize completion handler"""
        self.sqs_service = get_sqs_service()
        self.processed_messages = set()  # Track processed message IDs to avoid duplicates
    
    def check_for_completion_messages(self, session_id: str = "unknown") -> List[Dict[str, Any]]:
        """
        Check for completion messages from financial pipeline
        
        Args:
            session_id: Session ID for logging
            
        Returns:
            List of completion messages found
        """
        print(f"[Session {session_id}] 🔍 Checking for financial pipeline completion messages...")
        
        try:
            # Receive completion messages
            messages = self.sqs_service.receive_completion_messages(session_id)
            
            if not messages:
                print(f"[Session {session_id}] ℹ️ No completion messages found")
                return []
            
            # Filter out already processed messages
            new_messages = []
            for message in messages:
                message_id = message['message_id']
                if message_id not in self.processed_messages:
                    new_messages.append(message)
                else:
                    print(f"[Session {session_id}] ⚠️ Skipping already processed message: {message_id}")
            
            if new_messages:
                print(f"[Session {session_id}] 📨 Found {len(new_messages)} new completion messages")
            
            return new_messages
            
        except Exception as e:
            print(f"[Session {session_id}] ❌ Error checking for completion messages: {str(e)}")
            return []
    
    def process_completion_message(self, message: Dict[str, Any], session_id: str = "unknown") -> Dict[str, Any]:
        """
        Process a single completion message from financial pipeline
        
        Args:
            message: Completion message to process
            session_id: Session ID for logging
            
        Returns:
            Dictionary with processing results
        """
        try:
            message_id = message['message_id']
            receipt_handle = message['receipt_handle']
            message_body = message['body']
            
            print(f"[Session {session_id}] 🔄 Processing completion message: {message_id}")
            
            # SIMPLIFIED: Extract information from message (handle both formats)
            if 'data' in message_body:
                # Full format with data object
                plant_data = message_body.get('data', {})
                plant_name = plant_data.get('plant_name', 'Unknown Plant')
                financial_status = plant_data.get('status', 'completed')
                org_name = plant_data.get('org_name', 'Unknown Organization')
                uid = plant_data.get('uid', 'unknown')
            else:
                # Simple format - just status field
                financial_status = message_body.get('status', 'completed')
                plant_name = 'Financial Pipeline Completion'
                org_name = 'Financial System'
                uid = 'financial_completion'
            
            print(f"[Session {session_id}] 📋 Message details:")
            print(f"[Session {session_id}]   - Plant: {plant_name}")
            print(f"[Session {session_id}]   - Organization: {org_name}")
            print(f"[Session {session_id}]   - UID: {uid}")
            print(f"[Session {session_id}]   - Financial Status: {financial_status}")
            
            # Determine extraction status message
            if financial_status.lower() in ['completed', 'success', 'successful']:
                extraction_status = "Extraction Completed Successfully"
                status_type = "success"
            else:
                extraction_status = f"Extraction Completed with Status: {financial_status}"
                status_type = "completed_with_status"
            
            # Prepare additional data for backend team
            additional_data = {
                "organization_name": org_name,
                "organization_uid": uid,
                "financial_pipeline_status": financial_status,
                "financial_completion_time": message_body.get('timestamp', 'unknown'),
                "original_message_id": message_id
            }
            
            # Send completion message to backend team
            print(f"[Session {session_id}] 📤 Sending completion message to backend team...")
            
            backend_result = send_backend_completion_message(
                plant_name=plant_name,
                extraction_status=extraction_status,
                session_id=session_id,
                additional_data=additional_data
            )
            
            if backend_result['success']:
                print(f"[Session {session_id}] ✅ Backend completion message sent successfully!")
                print(f"[Session {session_id}]   - Backend Message ID: {backend_result['message_id']}")
                
                # Mark message as processed
                self.processed_messages.add(message_id)
                
                # Delete the processed message from financial pipeline queue
                if self.sqs_service.delete_processed_message(receipt_handle, session_id):
                    print(f"[Session {session_id}] ✅ Financial pipeline message deleted from queue")
                else:
                    print(f"[Session {session_id}] ⚠️ Failed to delete financial pipeline message")
                
                return {
                    "success": True,
                    "plant_name": plant_name,
                    "extraction_status": extraction_status,
                    "backend_message_id": backend_result['message_id'],
                    "financial_message_id": message_id,
                    "status_type": status_type,
                    "processed_at": datetime.now(timezone.utc).isoformat()
                }
            else:
                print(f"[Session {session_id}] ❌ Failed to send backend completion message")
                print(f"[Session {session_id}]   - Error: {backend_result.get('error', 'Unknown error')}")
                
                return {
                    "success": False,
                    "plant_name": plant_name,
                    "error": f"Backend message failed: {backend_result.get('error', 'Unknown error')}",
                    "financial_message_id": message_id
                }
                
        except Exception as e:
            print(f"[Session {session_id}] ❌ Error processing completion message: {str(e)}")
            return {
                "success": False,
                "error": f"Processing error: {str(e)}",
                "financial_message_id": message.get('message_id', 'unknown')
            }
    
    def process_all_completion_messages(self, session_id: str = "unknown") -> Dict[str, Any]:
        """
        Check for and process all available completion messages
        
        Args:
            session_id: Session ID for logging
            
        Returns:
            Dictionary with processing summary
        """
        print(f"[Session {session_id}] 🚀 Starting completion message processing...")
        
        # Check for completion messages
        messages = self.check_for_completion_messages(session_id)
        
        if not messages:
            return {
                "success": True,
                "messages_found": 0,
                "messages_processed": 0,
                "results": [],
                "summary": "No completion messages found"
            }
        
        # Process each message
        results = []
        successful_count = 0
        
        for i, message in enumerate(messages, 1):
            print(f"[Session {session_id}] 🔄 Processing message {i}/{len(messages)}")
            
            result = self.process_completion_message(message, session_id)
            results.append(result)
            
            if result['success']:
                successful_count += 1
                print(f"[Session {session_id}] ✅ Message {i} processed successfully")
            else:
                print(f"[Session {session_id}] ❌ Message {i} processing failed")
        
        # Summary
        summary = f"Processed {successful_count}/{len(messages)} completion messages successfully"
        
        print(f"[Session {session_id}] 📊 Processing complete: {summary}")
        
        return {
            "success": True,
            "messages_found": len(messages),
            "messages_processed": successful_count,
            "results": results,
            "summary": summary
        }
    
    def monitor_completion_messages(
        self, 
        session_id: str = "unknown", 
        duration_seconds: int = 300,
        check_interval: int = 30
    ) -> Dict[str, Any]:
        """
        Monitor for completion messages over a specified duration
        
        Args:
            session_id: Session ID for logging
            duration_seconds: How long to monitor (default: 5 minutes)
            check_interval: How often to check (default: 30 seconds)
            
        Returns:
            Dictionary with monitoring results
        """
        print(f"[Session {session_id}] 👁️ Starting completion message monitoring...")
        print(f"[Session {session_id}]   - Duration: {duration_seconds} seconds")
        print(f"[Session {session_id}]   - Check interval: {check_interval} seconds")
        
        start_time = time.time()
        end_time = start_time + duration_seconds
        total_processed = 0
        all_results = []
        
        while time.time() < end_time:
            # Process any available messages
            result = self.process_all_completion_messages(session_id)
            
            if result['messages_processed'] > 0:
                total_processed += result['messages_processed']
                all_results.extend(result['results'])
                print(f"[Session {session_id}] 📈 Total processed so far: {total_processed}")
            
            # Wait before next check
            remaining_time = end_time - time.time()
            if remaining_time > 0:
                sleep_time = min(check_interval, remaining_time)
                print(f"[Session {session_id}] ⏳ Waiting {sleep_time:.0f} seconds before next check...")
                time.sleep(sleep_time)
        
        print(f"[Session {session_id}] 🏁 Monitoring completed")
        print(f"[Session {session_id}] 📊 Total messages processed: {total_processed}")
        
        return {
            "success": True,
            "monitoring_duration": duration_seconds,
            "total_messages_processed": total_processed,
            "all_results": all_results,
            "summary": f"Monitored for {duration_seconds} seconds, processed {total_processed} completion messages"
        }

# Global completion handler instance
_completion_handler = None

def get_completion_handler() -> CompletionHandler:
    """
    Get or create global completion handler instance
    
    Returns:
        CompletionHandler instance
    """
    global _completion_handler
    if _completion_handler is None:
        _completion_handler = CompletionHandler()
    return _completion_handler

def process_completion_messages(session_id: str = "unknown") -> Dict[str, Any]:
    """
    Convenience function to process all available completion messages
    
    Args:
        session_id: Session ID for logging
        
    Returns:
        Dictionary with processing results
    """
    handler = get_completion_handler()
    return handler.process_all_completion_messages(session_id)
