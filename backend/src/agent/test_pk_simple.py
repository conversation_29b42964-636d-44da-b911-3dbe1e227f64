"""
Simple test for PK field fix logic without complex imports
"""

def test_pk_replacement_logic():
    """Test the PK field replacement logic"""
    
    print("🔑 TESTING PK FIELD REPLACEMENT LOGIC")
    print("=" * 45)
    
    # Test cases based on your actual data
    test_cases = [
        {
            "name": "Organization with 'default null'",
            "data": {
                "sk": "scraped#org_details",
                "cfpp_type": "Joint-Venture",
                "country_name": "India",
                "organization_name": "Jhajjar Power Limited",
                "pk": "default null",
            },
            "expected_result": "UID_REPLACED"
        },
        {
            "name": "Plant without pk field",
            "data": {
                "sk": "scraped#plant_details",
                "plant_name": "Jhajjar Power Plant",
                "capacity": "1320 MW",
                # No pk field
            },
            "expected_result": "UID_ADDED"
        },
        {
            "name": "Unit with 'default null'",
            "data": {
                "sk": "unit#coal#2#plant#1",
                "unit_number": "1",
                "plant_id": "0",
                "pk": "default null",
            },
            "expected_result": "UID_REPLACED"
        }
    ]
    
    test_uid = "ORG_IN_657FE5_51516770"
    
    print(f"🔧 Test UID: {test_uid}")
    
    results = []
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}️⃣ {test_case['name']}:")
        
        data = test_case['data'].copy()
        original_pk = data.get('pk', 'NOT_FOUND')
        
        print(f"   Before: pk = '{original_pk}'")
        
        # Apply the PK fix logic (same as in json_s3_storage.py)
        if test_uid:
            data["org_uid"] = test_uid
            
            if "pk" in data and data["pk"] == "default null":
                data["pk"] = test_uid
                result = "UID_REPLACED"
                print(f"   ✅ Replaced 'default null' with UID")
            elif "pk" in data:
                result = "EXISTING_VALUE"
                print(f"   ⚠️ pk exists but value is: '{data['pk']}'")
            else:
                data["pk"] = test_uid
                result = "UID_ADDED"
                print(f"   ✅ Added missing pk field with UID")
        else:
            result = "NO_UID"
            print(f"   ❌ No UID provided")
        
        final_pk = data.get('pk', 'NOT_FOUND')
        print(f"   After: pk = '{final_pk}'")
        print(f"   org_uid = '{data.get('org_uid', 'NOT_SET')}'")
        
        # Check if result matches expectation
        expected = test_case['expected_result']
        success = (result == expected and final_pk == test_uid)
        
        print(f"   Expected: {expected}, Got: {result}")
        print(f"   Success: {'✅' if success else '❌'}")
        
        results.append(success)
    
    # Summary
    all_passed = all(results)
    print(f"\n🎯 SUMMARY:")
    print(f"   Tests passed: {sum(results)}/{len(results)}")
    print(f"   All tests passed: {'✅' if all_passed else '❌'}")
    
    return all_passed

def test_your_actual_data():
    """Test with the exact data you provided"""
    
    print(f"\n\n📋 TESTING YOUR ACTUAL DATA")
    print("=" * 35)
    
    # Your exact organization JSON
    your_org_data = {
        "sk": "scraped#org_details",
        "cfpp_type": "Joint-Venture",
        "country_name": "India",
        "currency_in": "INR",
        "financial_year": "04-03",
        "organization_name": "Jhajjar Power Limited",
        "plants_count": 1,
        "plant_types": ["Coal"],
        "ppa_flag": "Plant-level",
        "province": "Haryana",
        "pk": "default null",
    }
    
    # Your exact unit JSON
    your_unit_data = {
        "sk": "unit#coal#2#plant#1",
        "unit_number": "1",
        "plant_id": "0",
        "pk": "default null",
    }
    
    test_uid = "ORG_IN_657FE5_51516770"
    
    print(f"🔧 Using UID: {test_uid}")
    
    # Test organization data
    print(f"\n🏢 Your Organization Data:")
    print(f"   Original pk: '{your_org_data['pk']}'")
    
    # Apply fix
    if your_org_data["pk"] == "default null":
        your_org_data["pk"] = test_uid
        your_org_data["org_uid"] = test_uid
        org_fixed = True
        print(f"   ✅ Fixed to: '{your_org_data['pk']}'")
    else:
        org_fixed = False
        print(f"   ❌ Not 'default null': '{your_org_data['pk']}'")
    
    # Test unit data
    print(f"\n🔧 Your Unit Data:")
    print(f"   Original pk: '{your_unit_data['pk']}'")
    
    # Apply fix
    if your_unit_data["pk"] == "default null":
        your_unit_data["pk"] = test_uid
        your_unit_data["org_uid"] = test_uid
        unit_fixed = True
        print(f"   ✅ Fixed to: '{your_unit_data['pk']}'")
    else:
        unit_fixed = False
        print(f"   ❌ Not 'default null': '{your_unit_data['pk']}'")
    
    # Show final JSON
    print(f"\n📊 FINAL RESULTS:")
    print(f"Organization JSON:")
    print(f"  pk: '{your_org_data['pk']}'")
    print(f"  org_uid: '{your_org_data.get('org_uid', 'NOT_SET')}'")
    
    print(f"\nUnit JSON:")
    print(f"  pk: '{your_unit_data['pk']}'")
    print(f"  org_uid: '{your_unit_data.get('org_uid', 'NOT_SET')}'")
    
    both_fixed = org_fixed and unit_fixed
    print(f"\nBoth fixed: {'✅' if both_fixed else '❌'}")
    
    return both_fixed

def diagnose_production_issue():
    """Diagnose why PK fields might still be 'default null' in production"""
    
    print(f"\n\n🔍 DIAGNOSING PRODUCTION ISSUE")
    print("=" * 40)
    
    print("❓ Possible reasons for 'default null' in production:")
    print("1. org_uid is empty when storage functions are called")
    print("2. The pk field has a different value than 'default null'")
    print("3. The storage functions are not being called at all")
    print("4. The fix logic is not executing properly")
    
    print(f"\n💡 DEBUGGING STEPS:")
    print("1. Check the debug logs I added to json_s3_storage.py")
    print("2. Look for these log messages:")
    print("   - '🔍 DEBUG: org_uid = ...'")
    print("   - '🔍 DEBUG: current pk = ...'")
    print("   - '✅ Replaced pk default null with UID'")
    print("   - '❌ No org_uid provided'")
    
    print(f"\n🔧 EXPECTED LOG FLOW:")
    print("For organization level:")
    print("  [Session X] 🔍 DEBUG: org_uid = 'ORG_IN_657FE5_51516770'")
    print("  [Session X] 🔍 DEBUG: current pk = 'default null'")
    print("  [Session X] ✅ Replaced pk 'default null' with UID: ORG_IN_657FE5_51516770")
    
    print(f"\n🚨 IF YOU SEE:")
    print("  - 'org_uid = ''' (empty) → UID generation failed")
    print("  - 'current pk = 'NOT_FOUND'' → pk field missing from template")
    print("  - 'pk field exists but value is: ...' → pk has unexpected value")
    print("  - No debug logs at all → storage functions not called")

def main():
    """Run all tests"""
    
    print("🚀 PK FIELD FIX TESTING")
    print("=" * 30)
    
    try:
        # Test 1: PK replacement logic
        test1_passed = test_pk_replacement_logic()
        
        # Test 2: Your actual data
        test2_passed = test_your_actual_data()
        
        # Diagnosis
        diagnose_production_issue()
        
        # Summary
        all_tests_passed = test1_passed and test2_passed
        
        print(f"\n\n🎯 FINAL SUMMARY")
        print("=" * 20)
        print(f"1. PK Replacement Logic: {'✅' if test1_passed else '❌'}")
        print(f"2. Your Actual Data: {'✅' if test2_passed else '❌'}")
        
        if all_tests_passed:
            print(f"\n🎉 ALL TESTS PASSED!")
            print("✅ The PK field fix logic is correct")
            print("✅ Your data should be fixed properly")
            print("\n🔍 If still seeing 'default null' in production:")
            print("   → Check the debug logs in json_s3_storage.py")
            print("   → Run a test extraction and look for the debug messages")
        else:
            print(f"\n❌ SOME TESTS FAILED!")
            print("The PK field fix logic needs adjustment.")
        
        return all_tests_passed
        
    except Exception as e:
        print(f"\n💥 TEST ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)