"""
JSON S3 Storage Module for Power Plant Research Data

This module handles uploading JSON data at different processing levels
(organization, plant, unit) to S3 with proper folder structure.
"""

import os
import json
import re
import boto3
from datetime import datetime
from typing import Dict, Any, Optional
from dotenv import load_dotenv

load_dotenv()

# S3 Configuration - Use S3-specific credentials
S3_AWS_ACCESS_KEY = os.getenv('S3_AWS_ACCESS_KEY_ID')
S3_AWS_SECRET_KEY = os.getenv('S3_AWS_SECRET_ACCESS_KEY')
AWS_REGION = os.getenv('AWS_REGION', 'ap-south-1')
S3_BUCKET = 'clem-transition-tech'

print(f"🔧 S3 Configuration:")
print(f"   - S3 Access Key: {S3_AWS_ACCESS_KEY[:8] + '...' if S3_AWS_ACCESS_KEY else 'NOT SET'}")
print(f"   - S3 Region: {AWS_REGION}")
print(f"   - S3 Bucket: {S3_BUCKET}")

def sanitize_plant_name(plant_name: str) -> str:
    """
    Convert power plant name to S3-safe folder name.
    
    Examples:
        "Jhajjar Power Plant" → "Jhajjar_Power_Plant"
        "NTPC Dadri (Stage-II)" → "NTPC_Dadri_Stage_II"
        "Adani Mundra Power Station" → "Adani_Mundra_Power_Station"
    """
    if not plant_name:
        return "Unknown_Plant"
    
    # Remove special characters except spaces and hyphens, replace & with _and_
    cleaned = plant_name.replace('&', '_and_')
    cleaned = re.sub(r'[^\w\s-]', '', cleaned)
    
    # Replace spaces and hyphens with underscores
    sanitized = cleaned.replace(' ', '_').replace('-', '_')
    
    # Remove multiple consecutive underscores
    sanitized = re.sub(r'_+', '_', sanitized)
    
    # Remove leading/trailing underscores
    sanitized = sanitized.strip('_')
    
    return sanitized if sanitized else "Unknown_Plant"

def upload_json_to_s3(
    json_data: Dict[Any, Any], 
    plant_folder: str, 
    filename: str,
    session_id: str = "unknown"
) -> Optional[str]:
    """
    Upload JSON data to S3 with the specified folder structure.
    
    Args:
        json_data: Dictionary containing the data to upload
        plant_folder: Sanitized plant name for folder structure
        filename: Name of the JSON file (e.g., 'organization_level.json')
        session_id: Session ID for logging
        
    Returns:
        S3 URL of uploaded file, or None if upload failed
    """
    try:
        # Initialize S3 client with S3-specific credentials
        s3_client = boto3.client(
            's3',
            aws_access_key_id=S3_AWS_ACCESS_KEY,
            aws_secret_access_key=S3_AWS_SECRET_KEY,
            region_name=AWS_REGION
        )
        
        # Add metadata to JSON
        enhanced_data = {
            **json_data,
            "metadata": {
                "uploaded_at": datetime.utcnow().isoformat() + "Z",
                "session_id": session_id,
                "plant_folder": plant_folder,
                "file_type": filename.replace('.json', ''),
                "bucket": S3_BUCKET
            }
        }
        
        # Convert to JSON string
        json_string = json.dumps(enhanced_data, indent=2, ensure_ascii=False)
        
        # Create S3 key (file path)
        s3_key = f"{plant_folder}/{filename}"
        
        # Upload to S3
        s3_client.put_object(
            Bucket=S3_BUCKET,
            Key=s3_key,
            Body=json_string,
            ContentType='application/json',
            ContentEncoding='utf-8'
        )
        
        # Generate S3 URL
        s3_url = f"https://{S3_BUCKET}.s3.amazonaws.com/{s3_key}"
        
        print(f"[Session {session_id}] ✅ JSON uploaded: {s3_url}")
        return s3_url
        
    except Exception as e:
        print(f"[Session {session_id}] ❌ Failed to upload {filename}: {str(e)}")
        return None

def store_organization_data(
    org_data: Dict[Any, Any], 
    plant_name: str, 
    session_id: str = "unknown",
    org_uid: str = None
) -> Optional[str]:
    """
    Store organization-level data to S3.
    
    Args:
        org_data: Organization data dictionary
        plant_name: Original plant name from user query
        session_id: Session ID for tracking
        org_uid: Organization UID (primary key)
        
    Returns:
        S3 URL of uploaded file
    """
    # Add UID to organization data as primary key
    print(f"[Session {session_id}] 🔍 DEBUG: org_uid = '{org_uid}'")
    print(f"[Session {session_id}] 🔍 DEBUG: org_data keys = {list(org_data.keys())}")
    print(f"[Session {session_id}] 🔍 DEBUG: current pk = '{org_data.get('pk', 'NOT_FOUND')}'")
    
    if org_uid:
        org_data["org_uid"] = org_uid
        # ALWAYS replace pk field with actual UID regardless of current value
        old_pk = org_data.get("pk", "NOT_FOUND")
        org_data["pk"] = org_uid
        print(f"[Session {session_id}] ✅ Set pk field: '{old_pk}' → '{org_uid}'")
        print(f"[Session {session_id}] 🔑 Added UID to organization data: {org_uid}")
    else:
        print(f"[Session {session_id}] ❌ No org_uid provided to store_organization_data")
        print(f"[Session {session_id}] 🔍 EMERGENCY FIX: Attempting to generate UID from org data...")

        # EMERGENCY FIX: Try to generate UID from organization data if not provided
        org_name = org_data.get("organization_name", "")
        country = org_data.get("country_name", "")

        if org_name and country:
            try:
                from agent.database_manager import get_database_manager
                db_manager = get_database_manager()
                emergency_uid = db_manager.generate_org_uid(org_name, country)

                org_data["org_uid"] = emergency_uid
                old_pk = org_data.get("pk", "NOT_FOUND")
                org_data["pk"] = emergency_uid
                print(f"[Session {session_id}] 🚨 EMERGENCY UID GENERATED: '{emergency_uid}'")
                print(f"[Session {session_id}] ✅ Set pk field: '{old_pk}' → '{emergency_uid}'")
            except Exception as e:
                print(f"[Session {session_id}] ❌ Emergency UID generation failed: {e}")
                # Last resort: set pk to null instead of "default null"
                org_data["pk"] = None
                print(f"[Session {session_id}] 🔧 Set pk to null as last resort")
        else:
            print(f"[Session {session_id}] ❌ Cannot generate emergency UID: missing org_name or country")
            # Last resort: set pk to null instead of "default null"
            org_data["pk"] = None
            print(f"[Session {session_id}] 🔧 Set pk to null as last resort")
    
    # CRITICAL FIX: Enforce fixed values for off_peak_hours and peak_hours right before storage
    # This ensures they are NEVER null regardless of what the LLM generated
    org_data["off_peak_hours"] = 0.466
    org_data["peak_hours"] = 0.9
    print(f"[Session {session_id}] 🔧 FINAL ENFORCEMENT: off_peak_hours=0.466, peak_hours=0.9")

    # NEW HIERARCHICAL STRUCTURE: {country_name}/{org_uid}/{org_uid}.json
    try:
        from agent.database_manager import get_database_manager
        db_manager = get_database_manager()

        # Get plant info from database to get country
        plant_info = db_manager.check_plant_exists(plant_name, "")  # Country will be found from plant_name
        if plant_info:
            country = plant_info.get("country", "Unknown")
            org_uid_from_db = plant_info.get("org_uid", org_uid)

            # Use full country name and org_uid for path
            s3_folder = f"{country}/{org_uid_from_db}"
            filename = f"{org_uid_from_db}.json"

            print(f"[Session {session_id}] 🏢 NEW STRUCTURE: Storing organization data")
            print(f"[Session {session_id}] 📁 S3 path: {s3_folder}/{filename}")

            return upload_json_to_s3(org_data, s3_folder, filename, session_id)
        else:
            # Fallback to old structure if database lookup fails
            print(f"[Session {session_id}] ⚠️ Database lookup failed, using fallback structure")
            plant_folder = sanitize_plant_name(plant_name)
            filename = "organization_level.json"
            return upload_json_to_s3(org_data, plant_folder, filename, session_id)

    except Exception as e:
        print(f"[Session {session_id}] ❌ Error with new S3 structure: {e}")
        # Fallback to old structure
        plant_folder = sanitize_plant_name(plant_name)
        filename = "organization_level.json"
        return upload_json_to_s3(org_data, plant_folder, filename, session_id)

def store_plant_data(
    plant_data: Dict[Any, Any], 
    plant_name: str, 
    session_id: str = "unknown",
    org_uid: str = None
) -> Optional[str]:
    """
    Store plant-level data to S3.
    
    Args:
        plant_data: Plant data dictionary
        plant_name: Original plant name from user query
        session_id: Session ID for tracking
        org_uid: Organization UID (primary key)
        
    Returns:
        S3 URL of uploaded file
    """
    # Plant data should already have plant_uid as pk from process_plant_data_formatting
    print(f"[Session {session_id}] 🔍 DEBUG: org_uid = '{org_uid}'")
    print(f"[Session {session_id}] 🔍 DEBUG: plant_data keys = {list(plant_data.keys())}")
    print(f"[Session {session_id}] 🔍 DEBUG: current pk = '{plant_data.get('pk', 'NOT_FOUND')}'")

    # Get plant_uid from the pk field (should be set by process_plant_data_formatting)
    plant_uid = plant_data.get("pk")
    if not plant_uid:
        print(f"[Session {session_id}] ❌ No plant_uid found in pk field")
        return None

    # Add org_uid for reference
    if org_uid:
        plant_data["org_uid"] = org_uid
        print(f"[Session {session_id}] 🔑 Added org_uid to plant data: {org_uid}")

    print(f"[Session {session_id}] ✅ Using plant_uid as pk: {plant_uid}")
    
    # NEW HIERARCHICAL STRUCTURE: {country_name}/{org_uid}/{plant_uid}/{plant_sk}.json
    try:
        from agent.database_manager import get_database_manager
        db_manager = get_database_manager()

        # Get plant info from database
        plant_info = db_manager.check_plant_exists(plant_name, "")
        if plant_info:
            country = plant_info.get("country", "Unknown")
            org_uid_from_db = plant_info.get("org_uid", org_uid)

            # Get plant_sk from plant_data
            plant_sk = plant_data.get("sk", "plant_unknown")

            # Use hierarchical path: country/org_uid/plant_uid/plant_sk.json
            s3_folder = f"{country}/{org_uid_from_db}/{plant_uid}"
            filename = f"{plant_sk}.json"

            print(f"[Session {session_id}] 🏭 NEW STRUCTURE: Storing plant data")
            print(f"[Session {session_id}] 📁 S3 path: {s3_folder}/{filename}")

            return upload_json_to_s3(plant_data, s3_folder, filename, session_id)
        else:
            # Fallback to old structure if database lookup fails
            print(f"[Session {session_id}] ⚠️ Database lookup failed, using fallback structure")
            plant_folder = sanitize_plant_name(plant_name)
            filename = "plant_level.json"
            return upload_json_to_s3(plant_data, plant_folder, filename, session_id)

    except Exception as e:
        print(f"[Session {session_id}] ❌ Error with new S3 structure: {e}")
        # Fallback to old structure
        plant_folder = sanitize_plant_name(plant_name)
        filename = "plant_level.json"
        return upload_json_to_s3(plant_data, plant_folder, filename, session_id)

def store_unit_data(
    unit_data: Dict[Any, Any], 
    plant_name: str, 
    unit_number: str,
    session_id: str = "unknown",
    org_uid: str = None
) -> Optional[str]:
    """
    Store individual unit data to S3.
    
    Args:
        unit_data: Unit data dictionary
        plant_name: Original plant name from user query
        unit_number: Unit number (e.g., "1", "2", "3")
        session_id: Session ID for tracking
        org_uid: Organization UID (primary key)
        
    Returns:
        S3 URL of uploaded file
    """
    # Unit data should already have plant_uid as pk from process_unit_data_formatting
    print(f"[Session {session_id}] 🔍 DEBUG: org_uid = '{org_uid}'")
    print(f"[Session {session_id}] 🔍 DEBUG: unit_data keys = {list(unit_data.keys())}")
    print(f"[Session {session_id}] 🔍 DEBUG: current pk = '{unit_data.get('pk', 'NOT_FOUND')}'")

    # Get plant_uid from the pk field (should be set by process_unit_data_formatting)
    plant_uid = unit_data.get("pk")
    if not plant_uid:
        print(f"[Session {session_id}] ❌ No plant_uid found in pk field for unit {unit_number}")
        return None

    # Add org_uid for reference
    if org_uid:
        unit_data["org_uid"] = org_uid
        print(f"[Session {session_id}] 🔑 Added org_uid to unit {unit_number} data: {org_uid}")

    print(f"[Session {session_id}] ✅ Using plant_uid as pk for unit {unit_number}: {plant_uid}")
    
    # NEW HIERARCHICAL STRUCTURE: {country_name}/{org_uid}/{plant_uid}/{unit_sk}.json
    try:
        from agent.database_manager import get_database_manager
        db_manager = get_database_manager()

        # Get plant info from database
        plant_info = db_manager.check_plant_exists(plant_name, "")
        if plant_info:
            country = plant_info.get("country", "Unknown")
            org_uid_from_db = plant_info.get("org_uid", org_uid)

            # Get unit_sk from unit_data
            unit_sk = unit_data.get("sk", f"unit_{unit_number}")

            # Use hierarchical path: country/org_uid/plant_uid/unit_sk.json
            s3_folder = f"{country}/{org_uid_from_db}/{plant_uid}"
            filename = f"{unit_sk}.json"

            print(f"[Session {session_id}] ⚡ NEW STRUCTURE: Storing Unit {unit_number} data")
            print(f"[Session {session_id}] 📁 S3 path: {s3_folder}/{filename}")

            return upload_json_to_s3(unit_data, s3_folder, filename, session_id)
        else:
            # Fallback to old structure if database lookup fails
            print(f"[Session {session_id}] ⚠️ Database lookup failed, using fallback structure")
            plant_folder = sanitize_plant_name(plant_name)
            filename = f"unit_{unit_number}.json"
            return upload_json_to_s3(unit_data, plant_folder, filename, session_id)

    except Exception as e:
        print(f"[Session {session_id}] ❌ Error with new S3 structure: {e}")
        # Fallback to old structure
        plant_folder = sanitize_plant_name(plant_name)
        filename = f"unit_{unit_number}.json"
        return upload_json_to_s3(unit_data, plant_folder, filename, session_id)

def store_transition_plan_data(
    plant_name: str,
    session_id: str = "unknown",
    org_uid: str = None
) -> Optional[str]:
    """
    Store Level-4 transition plan data to S3.

    Args:
        plant_name: Original plant name from user query
        session_id: Session ID for tracking
        org_uid: Organization UID (primary key)

    Returns:
        S3 URL of uploaded file
    """
    # Create Level-4 transition plan JSON structure
    transition_plan_data = {
        "pk": org_uid if org_uid else "",
        "sk": "transition_plan",
        "selected_plan_id": "",
        "transitionPlanStratName": ""
    }

    print(f"[Session {session_id}] 🔍 DEBUG: Creating Level-4 transition plan")
    print(f"[Session {session_id}] 🔍 DEBUG: org_uid = '{org_uid}'")
    print(f"[Session {session_id}] 🔍 DEBUG: transition_plan_data = {transition_plan_data}")

    if org_uid:
        print(f"[Session {session_id}] 🔑 Added UID to transition plan: {org_uid}")
    else:
        print(f"[Session {session_id}] ❌ No org_uid provided to store_transition_plan_data")

    plant_folder = sanitize_plant_name(plant_name)
    filename = "transition_plan.json"

    print(f"[Session {session_id}] 📋 Storing Level-4 transition plan for: {plant_name}")
    print(f"[Session {session_id}] 📁 S3 folder: {plant_folder}")

    return upload_json_to_s3(transition_plan_data, plant_folder, filename, session_id)

def get_plant_s3_urls(plant_name: str, session_id: str = "unknown") -> Dict[str, Any]:
    """
    Generate S3 URLs for all expected files of a plant (for state tracking).
    
    Args:
        plant_name: Original plant name from user query
        session_id: Session ID for tracking
        
    Returns:
        Dictionary with URL structure for state management
    """
    plant_folder = sanitize_plant_name(plant_name)
    base_url = f"https://{S3_BUCKET}.s3.amazonaws.com/{plant_folder}"
    
    return {
        "plant_folder": plant_folder,
        "plant_name": plant_name,
        "base_url": base_url,
        "organization": f"{base_url}/organization_level.json",
        "plant": f"{base_url}/plant_level.json",
        "units": {}  # Will be populated as units are processed
    }

def check_s3_connection(session_id: str = "test") -> bool:
    """
    Test S3 connection and credentials.
    
    Returns:
        True if connection successful, False otherwise
    """
    try:
        s3_client = boto3.client(
            's3',
            aws_access_key_id=S3_AWS_ACCESS_KEY,
            aws_secret_access_key=S3_AWS_SECRET_KEY,
            region_name=AWS_REGION
        )
        
        # Try to list objects (this will fail if credentials are wrong)
        s3_client.head_bucket(Bucket=S3_BUCKET)
        print(f"[Session {session_id}] ✅ S3 connection successful to bucket: {S3_BUCKET}")
        return True
        
    except Exception as e:
        print(f"[Session {session_id}] ❌ S3 connection failed: {str(e)}")
        return False

# For testing/debugging
if __name__ == "__main__":
    # Test sanitization
    test_names = [
        "Jhajjar Power Plant",
        "NTPC Dadri (Stage-II)",
        "Adani Mundra Power Station",
        "Tata Power Plant - Unit 1&2"
    ]
    
    print("Testing plant name sanitization:")
    for name in test_names:
        sanitized = sanitize_plant_name(name)
        print(f"'{name}' → '{sanitized}'")
    
    # Test S3 connection
    print("\nTesting S3 connection:")
    check_s3_connection()