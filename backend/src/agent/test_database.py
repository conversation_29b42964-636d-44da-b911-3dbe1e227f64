"""
Test script for database manager functionality
"""

import os
import sys
from database_manager import DatabaseManager, get_database_manager

def test_database_manager():
    """Test basic database manager functionality"""
    
    print("🧪 Testing Database Manager...")
    
    # Initialize database manager
    db_manager = get_database_manager()
    
    # Test connection
    print("\n1. Testing database connection...")
    if db_manager.test_connection():
        print("✅ Database connection successful")
    else:
        print("❌ Database connection failed")
        return False
    
    # Test UID generation
    print("\n2. Testing UID generation...")
    org_name = "NTPC Limited"
    country = "India"
    uid = db_manager.generate_org_uid(org_name, country)
    print(f"Generated UID: {uid}")
    
    # Test plant existence check (should be empty initially)
    print("\n3. Testing plant existence check...")
    plant_name = "Dadri Power Station"
    existing_plant = db_manager.check_plant_exists(plant_name, country)
    if existing_plant:
        print(f"✅ Found existing plant: {existing_plant}")
    else:
        print("ℹ️ Plant not found in database (expected for first run)")
    
    # Test saving organization plants
    print("\n4. Testing organization plant saving...")
    plants_list = [
        {"name": "Dadri Power Station", "status": "operational"},
        {"name": "Farakka Super Thermal Power Station", "status": "operational"},
        {"name": "Kahalgaon Super Thermal Power Station", "status": "operational"},
    ]
    
    success = db_manager.save_organization_plants(
        org_name=org_name,
        country=country,
        plants_list=plants_list,
        org_uid=uid,
        discovery_session_id="test_session_123",
        discovered_from_plant=plant_name
    )
    
    if success:
        print("✅ Successfully saved organization plants")
    else:
        print("❌ Failed to save organization plants")
        return False
    
    # Test plant existence check again (should find it now)
    print("\n5. Testing plant existence check after saving...")
    existing_plant = db_manager.check_plant_exists(plant_name, country)
    if existing_plant:
        print(f"✅ Found plant after saving: {existing_plant['plant_name']}")
        print(f"   Organization: {existing_plant['org_name']}")
        print(f"   UID: {existing_plant['org_uid']}")
    else:
        print("❌ Plant not found after saving")
        return False
    
    # Test getting plants by organization
    print("\n6. Testing get plants by organization...")
    org_plants = db_manager.get_plants_by_organization(org_name)
    print(f"Found {len(org_plants)} plants for {org_name}:")
    for plant in org_plants:
        print(f"   - {plant['plant_name']} ({plant['plant_status']})")
    
    print("\n🎉 All database tests passed!")
    return True

if __name__ == "__main__":
    try:
        test_database_manager()
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()