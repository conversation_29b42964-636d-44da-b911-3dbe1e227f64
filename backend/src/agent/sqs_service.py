"""
SQS Service Module for Financial Pipeline Communication

This module handles sending messages to the financial pipeline via AWS SQS FIFO queue.
Messages are sent after UID generation and database storage to trigger financial analysis.
"""

import os
import json
import boto3
import hashlib
from datetime import datetime
from typing import Dict, Any, Optional, List
from botocore.exceptions import ClientError, BotoCoreError

class SQSService:
    """Service class for handling SQS communication with financial pipeline"""
    
    def __init__(self):
        """Initialize SQS client with SQS-specific credentials from environment variables"""
        # Use SQS-specific credentials
        self.aws_access_key_id = os.getenv("SQS_AWS_ACCESS_KEY_ID")
        self.aws_secret_access_key = os.getenv("SQS_AWS_SECRET_ACCESS_KEY")
        self.aws_region = os.getenv("AWS_REGION", "ap-south-1")
        self.queue_url = os.getenv("SQS_QUEUE_URL")
        self.queue_name = os.getenv("SQS_QUEUE_NAME", "transition.fifo")

        print(f"🔧 SQS Configuration:")
        print(f"   - SQS Access Key: {self.aws_access_key_id[:8] + '...' if self.aws_access_key_id else 'NOT SET'}")
        print(f"   - SQS Region: {self.aws_region}")
        print(f"   - Queue URL: {self.queue_url}")

        # Validate required configuration
        if not all([self.aws_access_key_id, self.aws_secret_access_key, self.queue_url]):
            raise ValueError("Missing required SQS configuration in environment variables")

        # Initialize SQS client
        try:
            self.sqs_client = boto3.client(
                'sqs',
                aws_access_key_id=self.aws_access_key_id,
                aws_secret_access_key=self.aws_secret_access_key,
                region_name=self.aws_region
            )
            print(f"✅ SQS client initialized for region: {self.aws_region}")
        except Exception as e:
            print(f"❌ Failed to initialize SQS client: {str(e)}")
            raise
    
    def create_message_payload(
        self, 
        org_name: str, 
        plant_name: str, 
        country: str, 
        uid: str, 
        session_id: str = "unknown"
    ) -> Dict[str, Any]:
        """
        Create standardized message payload for financial pipeline
        
        Args:
            org_name: Organization name
            plant_name: Power plant name
            country: Country where plant is located
            uid: Generated unique identifier
            session_id: Session ID for tracking
            
        Returns:
            Dictionary containing formatted message payload
        """
        return {
            "message_type": "financial_pipeline_trigger",
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "session_id": session_id,
            "data": {
                "org_name": org_name,
                "plant_name": plant_name,
                "country": country,
                "uid": uid
            },
            "metadata": {
                "source": "technical_pipeline",
                "version": "1.0",
                "priority": "normal",
                "queue_name": self.queue_name
            }
        }
    
    def generate_message_group_id(self, plant_name: str) -> str:
        """
        Generate MessageGroupId for FIFO queue based on plant name
        
        Args:
            plant_name: Name of the power plant
            
        Returns:
            Sanitized plant name suitable for MessageGroupId
        """
        # Sanitize plant name for use as MessageGroupId
        # Remove special characters and spaces, convert to lowercase
        sanitized = "".join(c.lower() if c.isalnum() else "_" for c in plant_name)
        # Ensure it starts with alphanumeric character
        if not sanitized[0].isalnum():
            sanitized = "plant_" + sanitized
        # Limit length (MessageGroupId max length is 128 characters)
        return sanitized[:120]
    
    def generate_deduplication_id(self, message_payload: Dict[str, Any]) -> str:
        """
        Generate MessageDeduplicationId based on message content
        
        Args:
            message_payload: The message payload dictionary
            
        Returns:
            SHA-256 hash of key message components for deduplication
        """
        # Create deduplication string from key components
        dedup_components = [
            message_payload["data"]["org_name"],
            message_payload["data"]["plant_name"],
            message_payload["data"]["uid"],
            message_payload["session_id"]
        ]
        dedup_string = "|".join(dedup_components)
        
        # Generate SHA-256 hash
        return hashlib.sha256(dedup_string.encode('utf-8')).hexdigest()
    
    def send_financial_trigger_message(
        self, 
        org_name: str, 
        plant_name: str, 
        country: str, 
        uid: str, 
        session_id: str = "unknown"
    ) -> Dict[str, Any]:
        """
        Send trigger message to financial pipeline via SQS FIFO queue
        
        Args:
            org_name: Organization name
            plant_name: Power plant name
            country: Country where plant is located
            uid: Generated unique identifier
            session_id: Session ID for tracking
            
        Returns:
            Dictionary with success status and details
        """
        try:
            # Create message payload
            message_payload = self.create_message_payload(
                org_name, plant_name, country, uid, session_id
            )
            
            # Generate FIFO queue requirements
            message_group_id = self.generate_message_group_id(plant_name)
            message_deduplication_id = self.generate_deduplication_id(message_payload)
            
            print(f"[Session {session_id}] 📤 Sending financial trigger message:")
            print(f"[Session {session_id}]   - Organization: {org_name}")
            print(f"[Session {session_id}]   - Plant: {plant_name}")
            print(f"[Session {session_id}]   - Country: {country}")
            print(f"[Session {session_id}]   - UID: {uid}")
            print(f"[Session {session_id}]   - MessageGroupId: {message_group_id}")
            print(f"[Session {session_id}]   - DeduplicationId: {message_deduplication_id[:16]}...")
            
            # Send message to SQS FIFO queue
            response = self.sqs_client.send_message(
                QueueUrl=self.queue_url,
                MessageBody=json.dumps(message_payload, indent=2),
                MessageGroupId=message_group_id,
                MessageDeduplicationId=message_deduplication_id,
                MessageAttributes={
                    'MessageType': {
                        'StringValue': 'financial_pipeline_trigger',
                        'DataType': 'String'
                    },
                    'SourceSystem': {
                        'StringValue': 'technical_pipeline',
                        'DataType': 'String'
                    },
                    'PlantName': {
                        'StringValue': plant_name,
                        'DataType': 'String'
                    },
                    'OrganizationName': {
                        'StringValue': org_name,
                        'DataType': 'String'
                    }
                }
            )
            
            message_id = response.get('MessageId', 'unknown')
            sequence_number = response.get('SequenceNumber', 'unknown')
            
            print(f"[Session {session_id}] ✅ Financial trigger message sent successfully!")
            print(f"[Session {session_id}]   - MessageId: {message_id}")
            print(f"[Session {session_id}]   - SequenceNumber: {sequence_number}")
            
            return {
                "success": True,
                "message_id": message_id,
                "sequence_number": sequence_number,
                "message_group_id": message_group_id,
                "deduplication_id": message_deduplication_id,
                "timestamp": message_payload["timestamp"],
                "error": None
            }
            
        except ClientError as e:
            error_code = e.response['Error']['Code']
            error_message = e.response['Error']['Message']
            print(f"[Session {session_id}] ❌ AWS SQS ClientError: {error_code} - {error_message}")
            
            return {
                "success": False,
                "message_id": None,
                "error": f"SQS ClientError: {error_code} - {error_message}",
                "error_type": "client_error"
            }
            
        except BotoCoreError as e:
            print(f"[Session {session_id}] ❌ AWS BotoCoreError: {str(e)}")
            
            return {
                "success": False,
                "message_id": None,
                "error": f"BotoCoreError: {str(e)}",
                "error_type": "botocore_error"
            }
            
        except Exception as e:
            print(f"[Session {session_id}] ❌ Unexpected error sending SQS message: {str(e)}")
            
            return {
                "success": False,
                "message_id": None,
                "error": f"Unexpected error: {str(e)}",
                "error_type": "unexpected_error"
            }
    
    def receive_completion_messages(self, session_id: str = "unknown", max_messages: int = 10) -> List[Dict[str, Any]]:
        """
        Receive completion messages from financial pipeline

        Args:
            session_id: Session ID for logging
            max_messages: Maximum number of messages to receive

        Returns:
            List of received messages
        """
        try:
            print(f"[Session {session_id}] 📥 Checking for completion messages...")

            # Receive messages from SQS queue
            response = self.sqs_client.receive_message(
                QueueUrl=self.queue_url,
                MaxNumberOfMessages=max_messages,
                WaitTimeSeconds=5,  # Short polling
                MessageAttributeNames=['All'],
                AttributeNames=['All']
            )

            messages = response.get('Messages', [])

            if not messages:
                print(f"[Session {session_id}] ℹ️ No completion messages found")
                return []

            print(f"[Session {session_id}] 📨 Received {len(messages)} messages")

            completion_messages = []
            for message in messages:
                try:
                    # Parse message body
                    message_body = json.loads(message['Body'])
                    message_attributes = message.get('MessageAttributes', {})

                    # SIMPLIFIED: Check if this is a completion message
                    # Accept both attribute-based and simple JSON detection
                    message_type = message_attributes.get('MessageType', {}).get('StringValue', '')

                    # Check for simple completion message format
                    is_completion = (
                        message_type == 'financial_pipeline_completion' or  # Attribute-based
                        'status' in message_body  # Simple JSON with status field
                    )

                    if is_completion:
                        completion_messages.append({
                            'message_id': message['MessageId'],
                            'receipt_handle': message['ReceiptHandle'],
                            'body': message_body,
                            'attributes': message_attributes,
                            'timestamp': message_body.get('timestamp', 'unknown')
                        })

                        print(f"[Session {session_id}] ✅ Found completion message:")
                        print(f"[Session {session_id}]   - Message ID: {message['MessageId']}")
                        print(f"[Session {session_id}]   - Status: {message_body.get('status', 'unknown')}")
                        print(f"[Session {session_id}]   - Format: {'Simple JSON' if 'status' in message_body else 'Full format'}")

                except json.JSONDecodeError as e:
                    print(f"[Session {session_id}] ⚠️ Failed to parse message body: {e}")
                    continue
                except Exception as e:
                    print(f"[Session {session_id}] ⚠️ Error processing message: {e}")
                    continue

            return completion_messages

        except Exception as e:
            print(f"[Session {session_id}] ❌ Error receiving completion messages: {str(e)}")
            return []

    def delete_processed_message(self, receipt_handle: str, session_id: str = "unknown") -> bool:
        """
        Delete a processed message from the queue

        Args:
            receipt_handle: Receipt handle of the message to delete
            session_id: Session ID for logging

        Returns:
            True if deletion successful, False otherwise
        """
        try:
            self.sqs_client.delete_message(
                QueueUrl=self.queue_url,
                ReceiptHandle=receipt_handle
            )

            print(f"[Session {session_id}] ✅ Message deleted from queue")
            return True

        except Exception as e:
            print(f"[Session {session_id}] ❌ Error deleting message: {str(e)}")
            return False

    def test_connection(self, session_id: str = "test") -> bool:
        """
        Test SQS connection and queue accessibility

        Args:
            session_id: Session ID for logging

        Returns:
            True if connection successful, False otherwise
        """
        try:
            # Get queue attributes to test connection
            response = self.sqs_client.get_queue_attributes(
                QueueUrl=self.queue_url,
                AttributeNames=['QueueArn', 'VisibilityTimeout']
            )

            queue_arn = response['Attributes'].get('QueueArn', 'unknown')
            print(f"[Session {session_id}] ✅ SQS connection test successful!")
            print(f"[Session {session_id}]   - Queue ARN: {queue_arn}")

            return True

        except Exception as e:
            print(f"[Session {session_id}] ❌ SQS connection test failed: {str(e)}")
            return False

# Global SQS service instance
_sqs_service = None

def get_sqs_service() -> SQSService:
    """
    Get or create global SQS service instance
    
    Returns:
        SQSService instance
    """
    global _sqs_service
    if _sqs_service is None:
        _sqs_service = SQSService()
    return _sqs_service

def send_financial_pipeline_trigger(
    org_name: str, 
    plant_name: str, 
    country: str, 
    uid: str, 
    session_id: str = "unknown"
) -> Dict[str, Any]:
    """
    Convenience function to send financial pipeline trigger message
    
    Args:
        org_name: Organization name
        plant_name: Power plant name
        country: Country where plant is located
        uid: Generated unique identifier
        session_id: Session ID for tracking
        
    Returns:
        Dictionary with success status and details
    """
    try:
        sqs_service = get_sqs_service()
        return sqs_service.send_financial_trigger_message(
            org_name, plant_name, country, uid, session_id
        )
    except Exception as e:
        print(f"[Session {session_id}] ❌ Failed to get SQS service: {str(e)}")
        return {
            "success": False,
            "message_id": None,
            "error": f"Service initialization error: {str(e)}",
            "error_type": "service_error"
        }