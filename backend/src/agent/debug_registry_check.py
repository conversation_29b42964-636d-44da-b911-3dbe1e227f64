"""
Debug script to check why registry check is failing for Jhajjar Power Plant
"""

from database_manager import get_database_manager

def test_registry_check():
    """Test the exact registry check logic"""
    
    print("🔍 DEBUGGING REGISTRY CHECK FOR JHAJJAR")
    print("=" * 50)
    
    db_manager = get_database_manager()
    
    # Test different variations of the plant name
    test_names = [
        "Jhajjar Power Plant",
        "jhajjar power plant",
        "Jhajjar Power Plant ",  # with trailing space
        " Jhajjar Power Plant",  # with leading space
        "Jhajjar Power Plant.",  # with period
        "Jhajjar Power Station",  # different suffix
    ]
    
    for plant_name in test_names:
        print(f"\n🧪 Testing: '{plant_name}'")
        result = db_manager.check_plant_exists(plant_name)
        
        if result:
            print(f"✅ FOUND: {result['plant_name']}")
            print(f"   Organization: {result['org_name']}")
            print(f"   UID: {result['org_uid']}")
        else:
            print("❌ NOT FOUND")
    
    # Check what's actually in the database
    print(f"\n📊 WHAT'S IN DATABASE:")
    session = db_manager.get_session()
    try:
        from database_manager import PowerPlantRegistry
        all_plants = session.query(PowerPlantRegistry).all()
        
        for plant in all_plants:
            if "jhajjar" in plant.plant_name.lower():
                print(f"   Database entry: '{plant.plant_name}'")
                print(f"   Organization: {plant.org_name}")
                print(f"   UID: {plant.org_uid}")
                print(f"   Country: {plant.country}")
    finally:
        session.close()

if __name__ == "__main__":
    test_registry_check()