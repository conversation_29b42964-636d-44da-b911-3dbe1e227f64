"""
Test the complete flow for Jhajjar Power Plant to debug the issues
"""

import sys
from typing import Dict, Any

# Mock state and functions for testing
class MockState(dict):
    pass

def mock_get_research_topic(messages):
    if messages and len(messages) > 0:
        content = messages[0].get("content", "")
        if "Jhajjar Power Plant" in content:
            return "Jhajjar Power Plant"
    return ""

def test_jhajjar_flow():
    """Test the complete flow for Jhajjar Power Plant"""
    
    print("🧪 TESTING JHAJJAR POWER PLANT FLOW")
    print("=" * 50)
    
    # Step 1: Create initial state
    state = MockState({
        "messages": [{"content": "Extract information about Jhajjar Power Plant"}],
        "session_id": "test_jhajjar_123",
        "search_phase": 1,
        "research_loop_count": 0,
        "web_research_result": [],
        "search_query": [],
        "sources_gathered": [],
        "org_level_complete": False,
        "continue_research": False,
        "phase_complete": False,
        "initial_search_query_count": 3,
        "max_research_loops": 2,
        "reasoning_model": "gemini-2.0-flash-exp",
    })
    
    print("1️⃣ INITIAL STATE:")
    print(f"   Plant query: {mock_get_research_topic(state['messages'])}")
    print(f"   Session ID: {state['session_id']}")
    
    # Step 2: Registry Check
    print("\n2️⃣ REGISTRY CHECK:")
    from database_manager import get_database_manager
    
    db_manager = get_database_manager()
    plant_name = mock_get_research_topic(state["messages"])
    existing_plant = db_manager.check_plant_exists(plant_name)
    
    if existing_plant:
        print(f"✅ Plant found in registry!")
        print(f"   Organization: {existing_plant['org_name']}")
        print(f"   UID: {existing_plant['org_uid']}")
        print(f"   Country: {existing_plant['country']}")
        
        # Update state with existing plant info
        state.update({
            "plant_exists_in_db": True,
            "existing_plant_info": existing_plant,
            "org_uid": existing_plant["org_uid"],
            "org_name": existing_plant["org_name"],
            "plant_country": existing_plant["country"],
            "registry_check_complete": True,
            "registry_error": ""
        })
        
        # Should route to generate_uid (but use existing UID)
        next_node = "generate_uid"
        print(f"   → Route to: {next_node}")
        
    else:
        print("❌ Plant not found (this shouldn't happen!)")
        return False
    
    # Step 3: UID Generation (should use existing)
    print("\n3️⃣ UID GENERATION:")
    org_name = state.get("org_name", "")
    country = state.get("plant_country", "")
    existing_uid = state.get("org_uid", "")
    
    print(f"   Organization: {org_name}")
    print(f"   Country: {country}")
    print(f"   Existing UID: {existing_uid}")
    
    if existing_uid:
        print(f"✅ Using existing UID: {existing_uid}")
        final_uid = existing_uid
    else:
        print("❌ No existing UID found (this shouldn't happen!)")
        return False
    
    state.update({
        "org_uid": final_uid,
        "uid_generation_complete": True,
        "uid_error": ""
    })
    
    # Step 4: Routing after UID generation
    print("\n4️⃣ ROUTING AFTER UID GENERATION:")
    discovered_plants = state.get("discovered_plants", [])
    print(f"   Discovered plants: {len(discovered_plants)}")
    
    if discovered_plants and len(discovered_plants) > 0:
        next_node = "populate_database_async"
        print(f"   → Route to: {next_node} (save new plants)")
    else:
        next_node = "trigger_financial_pipeline"
        print(f"   → Route to: {next_node} (skip database, plant exists)")
    
    # Step 5: Financial Pipeline Trigger
    print("\n5️⃣ FINANCIAL PIPELINE TRIGGER:")
    org_uid = state.get("org_uid", "")
    org_name = state.get("org_name", "")
    country = state.get("plant_country", "")
    plant_name = mock_get_research_topic(state.get("messages", []))
    
    print(f"   UID: {org_uid}")
    print(f"   Organization: {org_name}")
    print(f"   Plant: {plant_name}")
    print(f"   Country: {country}")
    
    if org_uid:
        financial_payload = {
            "org_uid": org_uid,
            "org_name": org_name,
            "country": country,
            "plant_name": plant_name,
            "session_id": state.get("session_id"),
            "s3_bucket_path": f"financial-data/{org_uid}/",
            "trigger_timestamp": 1234567890
        }
        
        print("✅ Financial pipeline triggered successfully")
        print(f"   Payload UID: {financial_payload['org_uid']}")
        
        state.update({
            "financial_pipeline_triggered": True,
            "financial_payload": financial_payload,
            "financial_trigger_error": ""
        })
    else:
        print("❌ No UID for financial pipeline")
        return False
    
    # Step 6: Continue to existing pipeline
    print("\n6️⃣ CONTINUE TO EXISTING PIPELINE:")
    print(f"   UID will be available in state: {state.get('org_uid')}")
    print(f"   Organization: {state.get('org_name')}")
    print(f"   This UID should be used as PK in all 3-level extraction")
    
    # Summary
    print("\n✅ FLOW COMPLETED SUCCESSFULLY!")
    print("=" * 50)
    print(f"Final UID: {state.get('org_uid')}")
    print(f"Organization: {state.get('org_name')}")
    print(f"Financial pipeline: {'✅' if state.get('financial_pipeline_triggered') else '❌'}")
    print(f"Database population: {'⏭️ Skipped (plant exists)' if not state.get('discovered_plants') else '✅ Completed'}")
    
    return True

def main():
    """Run the test"""
    
    try:
        success = test_jhajjar_flow()
        if success:
            print("\n🎉 TEST PASSED - Flow working correctly!")
        else:
            print("\n❌ TEST FAILED - Issues found!")
        return success
    except Exception as e:
        print(f"\n💥 TEST ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)