"""
End-to-End Test for Plant Registry System

This script tests the complete registry workflow:
1. Database setup and connection
2. Plant registry check
3. Quick organization discovery
4. UID generation
5. Database population
6. Financial pipeline trigger simulation
"""

import os
import sys
from typing import Dict, Any
from langchain_core.messages import HumanMessage

# Add the agent directory to Python path
sys.path.append(os.path.dirname(__file__))

from database_manager import get_database_manager
from registry_nodes import (
    check_plant_registry,
    quick_org_discovery_node,
    generate_uid_node,
    trigger_financial_pipeline_node,
    populate_database_async_node
)
from state import OverallState

def create_test_state(plant_name: str) -> Dict[str, Any]:
    """
    Create a test state for the registry system
    
    Args:
        plant_name: Name of the power plant to test
        
    Returns:
        Test state dictionary
    """
    return {
        "messages": [HumanMessage(content=f"Extract information about {plant_name}")],
        "session_id": "test_session_123",
        "search_phase": 1,
        "research_loop_count": 0,
        "web_research_result": [],
        "search_query": [],
        "sources_gathered": [],
        "org_level_complete": False,
        "continue_research": False,
        "phase_complete": False,
        "initial_search_query_count": 3,  # Reduced for testing
        "max_research_loops": 2,
        "reasoning_model": "gemini-2.0-flash-exp",
    }

def test_registry_workflow():
    """Test the complete registry workflow"""
    
    print("🧪 TESTING COMPLETE PLANT REGISTRY SYSTEM")
    print("=" * 60)
    
    # Test cases
    test_plants = [
        "Dadri Power Station",  # New plant (will trigger discovery)
        "Farakka Super Thermal Power Station",  # Should be found after first test
    ]
    
    for i, plant_name in enumerate(test_plants, 1):
        print(f"\n🔬 TEST CASE {i}: {plant_name}")
        print("-" * 40)
        
        # Create test state
        state = create_test_state(plant_name)
        
        # Step 1: Check plant registry
        print("\n1️⃣ STEP 1: Plant Registry Check")
        registry_result = check_plant_registry(state)
        state.update(registry_result)
        
        print(f"   Plant exists in DB: {state.get('plant_exists_in_db', False)}")
        if state.get('existing_plant_info'):
            print(f"   Existing UID: {state['existing_plant_info'].get('org_uid', 'N/A')}")
        
        # Step 2: Quick organization discovery (if needed)
        if not state.get('plant_exists_in_db', False):
            print("\n2️⃣ STEP 2: Quick Organization Discovery")
            # Mock the discovery for testing (since we don't want to make real API calls)
            discovery_result = {
                "quick_discovery_complete": True,
                "discovered_org_info": {
                    "org_name": "NTPC Limited",
                    "country": "India",
                    "plants": [
                        {"name": "Dadri Power Station", "status": "operational"},
                        {"name": "Farakka Super Thermal Power Station", "status": "operational"},
                        {"name": "Kahalgaon Super Thermal Power Station", "status": "operational"},
                    ]
                },
                "org_name": "NTPC Limited",
                "plant_country": "India",
                "discovered_plants": [
                    {"name": "Dadri Power Station", "status": "operational"},
                    {"name": "Farakka Super Thermal Power Station", "status": "operational"},
                    {"name": "Kahalgaon Super Thermal Power Station", "status": "operational"},
                ],
                "discovery_error": ""
            }
            state.update(discovery_result)
            print(f"   Organization: {state.get('org_name', 'N/A')}")
            print(f"   Country: {state.get('plant_country', 'N/A')}")
            print(f"   Plants discovered: {len(state.get('discovered_plants', []))}")
        else:
            print("\n2️⃣ STEP 2: Quick Organization Discovery (SKIPPED - Plant exists)")
            # Use existing plant info
            existing_info = state.get('existing_plant_info', {})
            state.update({
                "org_name": existing_info.get('org_name', ''),
                "plant_country": existing_info.get('country', ''),
                "org_uid": existing_info.get('org_uid', '')
            })
        
        # Step 3: Generate UID (if needed)
        if not state.get('org_uid'):
            print("\n3️⃣ STEP 3: UID Generation")
            uid_result = generate_uid_node(state)
            state.update(uid_result)
            print(f"   Generated UID: {state.get('org_uid', 'N/A')}")
        else:
            print(f"\n3️⃣ STEP 3: UID Generation (SKIPPED - Using existing: {state.get('org_uid')})")
        
        # Step 4: Trigger financial pipeline
        print("\n4️⃣ STEP 4: Financial Pipeline Trigger")
        financial_result = trigger_financial_pipeline_node(state)
        state.update(financial_result)
        print(f"   Financial pipeline triggered: {state.get('financial_pipeline_triggered', False)}")
        if state.get('financial_payload'):
            print(f"   Payload UID: {state['financial_payload'].get('org_uid', 'N/A')}")
        
        # Step 5: Populate database (if new discovery)
        if state.get('discovered_plants') and not state.get('plant_exists_in_db', False):
            print("\n5️⃣ STEP 5: Database Population")
            db_result = populate_database_async_node(state)
            state.update(db_result)
            print(f"   Database population complete: {state.get('database_population_complete', False)}")
            print(f"   Plants saved: {state.get('plants_saved_count', 0)}")
        else:
            print("\n5️⃣ STEP 5: Database Population (SKIPPED - No new plants)")
        
        # Summary
        print(f"\n✅ TEST CASE {i} COMPLETE")
        print(f"   Final UID: {state.get('org_uid', 'N/A')}")
        print(f"   Organization: {state.get('org_name', 'N/A')}")
        print(f"   Financial pipeline: {'✅' if state.get('financial_pipeline_triggered') else '❌'}")
        print(f"   Database updated: {'✅' if state.get('database_population_complete') else '➖'}")

def test_database_queries():
    """Test database query functionality"""
    
    print("\n\n🔍 TESTING DATABASE QUERIES")
    print("=" * 40)
    
    db_manager = get_database_manager()
    
    # Test 1: Check for plants
    print("\n1. Testing plant existence checks:")
    test_plants = ["Dadri Power Station", "Nonexistent Plant", "Farakka"]
    
    for plant in test_plants:
        result = db_manager.check_plant_exists(plant)
        if result:
            print(f"   ✅ {plant}: Found (UID: {result['org_uid']})")
        else:
            print(f"   ❌ {plant}: Not found")
    
    # Test 2: Get plants by organization
    print("\n2. Testing organization plant lookup:")
    org_plants = db_manager.get_plants_by_organization("NTPC Limited")
    print(f"   Found {len(org_plants)} plants for NTPC Limited:")
    for plant in org_plants:
        print(f"     - {plant['plant_name']} ({plant['plant_status']})")
    
    # Test 3: Get organization by UID
    print("\n3. Testing UID lookup:")
    if org_plants:
        test_uid = org_plants[0]['org_uid']
        org_info = db_manager.get_organization_by_uid(test_uid)
        if org_info:
            print(f"   ✅ UID {test_uid}: {org_info['org_name']} in {org_info['country']}")
        else:
            print(f"   ❌ UID {test_uid}: Not found")

def test_performance():
    """Test system performance"""
    
    print("\n\n⚡ TESTING SYSTEM PERFORMANCE")
    print("=" * 40)
    
    import time
    
    # Test registry check performance
    start_time = time.time()
    state = create_test_state("Dadri Power Station")
    registry_result = check_plant_registry(state)
    registry_time = time.time() - start_time
    
    print(f"Registry check time: {registry_time:.3f} seconds")
    
    # Test UID generation performance
    start_time = time.time()
    db_manager = get_database_manager()
    uid = db_manager.generate_org_uid("Test Organization", "Test Country")
    uid_time = time.time() - start_time
    
    print(f"UID generation time: {uid_time:.3f} seconds")
    print(f"Generated UID: {uid}")
    
    # Performance summary
    total_time = registry_time + uid_time
    print(f"\nTotal core operations time: {total_time:.3f} seconds")
    if total_time < 1.0:
        print("✅ Performance: Excellent (< 1 second)")
    elif total_time < 3.0:
        print("✅ Performance: Good (< 3 seconds)")
    else:
        print("⚠️ Performance: Needs optimization (> 3 seconds)")

def main():
    """Run all tests"""
    
    print("🚀 PLANT REGISTRY SYSTEM - COMPREHENSIVE TEST SUITE")
    print("=" * 80)
    
    try:
        # Test 1: Database connection
        print("\n📊 Testing database connection...")
        db_manager = get_database_manager()
        if not db_manager.test_connection():
            print("❌ Database connection failed - aborting tests")
            return False
        
        # Test 2: Registry workflow
        test_registry_workflow()
        
        # Test 3: Database queries
        test_database_queries()
        
        # Test 4: Performance
        test_performance()
        
        print("\n\n🎉 ALL TESTS COMPLETED SUCCESSFULLY!")
        print("=" * 80)
        print("✅ Database: Connected and operational")
        print("✅ Registry: Plant lookup working")
        print("✅ Discovery: Organization discovery ready")
        print("✅ UID System: Unique ID generation working")
        print("✅ Financial: Pipeline trigger ready")
        print("✅ Population: Database updates working")
        print("\n🚀 System is ready for production integration!")
        
        return True
        
    except Exception as e:
        print(f"\n❌ TEST SUITE FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)