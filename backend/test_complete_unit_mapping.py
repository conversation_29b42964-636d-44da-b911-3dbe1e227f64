#!/usr/bin/env python3
"""
Test script for complete unit mapping fix - both plant level and unit level
"""

import sys
import os
sys.path.append('src')

def test_complete_taichung_mapping():
    """Test that both plant level and unit level use sequential mapping"""
    print("🏭 Testing Complete Taichung Unit Mapping (Plant + Unit Level)...")
    
    try:
        # Simulate the complete process
        
        def simulate_plant_data_processing(plant_data: dict, session_id: str):
            """Simulate the fixed plant data processing"""
            
            print(f"[{session_id}] 🔧 Processing plant data formatting...")
            print(f"[{session_id}] 📊 INPUT units_id: {plant_data.get('units_id', 'NOT_FOUND')}")
            
            # 4. Process units_id - use sequential mapping to avoid duplicates
            units_id = plant_data.get("units_id", [])
            if isinstance(units_id, list):
                # CRITICAL FIX: Create sequential mapping for units_id to avoid duplicates
                # For Taichung: [1,2,3,4,5,6,7,8,9,10,1,2,3,4] → [1,2,3,4,5,6,7,8,9,10,11,12,13,14]
                
                print(f"[{session_id}] 🔧 Original units_id: {units_id}")
                
                # Convert all units to sequential numbers (1, 2, 3, ..., N)
                sequential_units = [i+1 for i in range(len(units_id))]
                plant_data["units_id"] = sequential_units
                
                print(f"[{session_id}] ✅ Sequential units_id: {sequential_units}")
                print(f"[{session_id}] 🗺️ Mapping: {dict(zip(sequential_units, [str(u) for u in units_id]))}")
                
                # Store the original-to-sequential mapping for reference
                original_to_sequential = {i+1: str(units_id[i]) for i in range(len(units_id))}
                print(f"[{session_id}] 📋 Original unit mapping: {original_to_sequential}")
            
            return plant_data
        
        def simulate_unit_detection(units_id_from_plant: list, session_id: str):
            """Simulate unit detection that matches plant level"""
            
            # The unit detection should produce the same sequential numbers as plant level
            sequential_numbers = [str(i+1) for i in range(len(units_id_from_plant))]
            unit_name_mapping = {i+1: f"Unit {units_id_from_plant[i]}" for i in range(len(units_id_from_plant))}
            
            print(f"[{session_id}] 🎯 Unit detection sequential numbers: {sequential_numbers}")
            print(f"[{session_id}] 🗺️ Unit name mapping: {unit_name_mapping}")
            
            return sequential_numbers, unit_name_mapping
        
        # Test case: Taichung Power Station
        original_plant_data = {
            "sk": "plant#coal#1",
            "name": "Taichung Power Station",
            "plant_id": 1,
            "plant_type": "coal",
            "lat": 24.2128,
            "long": 120.4818,
            "plant_address": "Taichung, Taiwan",
            "units_id": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 1, 2, 3, 4],  # Original with duplicates
            "grid_connectivity_maps": [],
            "ppa_details": [],
            "plant_images": [],
            "pk": "default null"
        }
        
        print(f"🔍 Test: Complete Taichung Power Station Mapping")
        print(f"   Original units_id: {original_plant_data['units_id']}")
        
        # Step 1: Process plant data (fix plant level)
        processed_plant_data = simulate_plant_data_processing(original_plant_data.copy(), "test_session")
        
        # Step 2: Process unit detection (should match plant level)
        original_units = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 1, 2, 3, 4]  # Original from extraction
        unit_sequential, unit_mapping = simulate_unit_detection(original_units, "test_session")
        
        print(f"\n🔍 Results Comparison:")
        print(f"   Plant level units_id: {processed_plant_data['units_id']}")
        print(f"   Unit level sequential: {[int(x) for x in unit_sequential]}")
        print(f"   Unit name mapping: {unit_mapping}")
        
        # Verify that plant level and unit level match
        plant_level_units = processed_plant_data['units_id']
        unit_level_units = [int(x) for x in unit_sequential]
        
        assert plant_level_units == unit_level_units, f"Plant and unit levels should match: {plant_level_units} vs {unit_level_units}"
        
        # Verify the expected results
        expected_sequential = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14]
        assert plant_level_units == expected_sequential, f"Expected {expected_sequential}, got {plant_level_units}"
        
        # Verify unit mapping preserves original unit information
        expected_mapping = {
            1: 'Unit 1', 2: 'Unit 2', 3: 'Unit 3', 4: 'Unit 4', 5: 'Unit 5',
            6: 'Unit 6', 7: 'Unit 7', 8: 'Unit 8', 9: 'Unit 9', 10: 'Unit 10',
            11: 'Unit 1', 12: 'Unit 2', 13: 'Unit 3', 14: 'Unit 4'  # Duplicates preserved
        }
        assert unit_mapping == expected_mapping, f"Expected {expected_mapping}, got {unit_mapping}"
        
        print(f"   ✅ Plant and unit levels perfectly aligned!")
        print(f"   ✅ All 14 units mapped sequentially")
        print(f"   ✅ Original unit information preserved")
        
        return True
        
    except Exception as e:
        print(f"❌ Complete Taichung mapping test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_final_json_structure():
    """Test the final JSON structure with correct sequential mapping"""
    print("\n📋 Testing Final JSON Structure...")
    
    try:
        # Expected final structure for Taichung Power Station
        expected_structure = {
            "organization_level": {
                "pk": "ORG_TW_TAIPOWER_12345678",
                "org_uid": "ORG_TW_TAIPOWER_12345678",
                "organization_name": "Taiwan Power Company",
                "country_name": "Taiwan"
            },
            "plant_level": {
                "name": "Taichung Power Station",
                "units_id": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14],  # ✅ Sequential!
                "pk": "ORG_TW_TAIPOWER_12345678",
                "grid_connectivity_maps": []  # Will be populated by enhancement
            },
            "unit_level": [
                {"unit_number": 1, "original_unit_name": "Unit 1", "pk": "ORG_TW_TAIPOWER_12345678"},
                {"unit_number": 2, "original_unit_name": "Unit 2", "pk": "ORG_TW_TAIPOWER_12345678"},
                # ... units 3-10 ...
                {"unit_number": 11, "original_unit_name": "Unit 1", "pk": "ORG_TW_TAIPOWER_12345678"},  # Duplicate
                {"unit_number": 12, "original_unit_name": "Unit 2", "pk": "ORG_TW_TAIPOWER_12345678"},  # Duplicate
                {"unit_number": 13, "original_unit_name": "Unit 3", "pk": "ORG_TW_TAIPOWER_12345678"},  # Duplicate
                {"unit_number": 14, "original_unit_name": "Unit 4", "pk": "ORG_TW_TAIPOWER_12345678"}   # Duplicate
            ]
        }
        
        print(f"🔍 Expected Structure Validation:")
        
        # Validate plant level
        plant_units = expected_structure["plant_level"]["units_id"]
        print(f"   Plant level units_id: {plant_units}")
        assert len(plant_units) == 14, f"Should have 14 units, got {len(plant_units)}"
        assert plant_units == list(range(1, 15)), f"Should be sequential 1-14, got {plant_units}"
        
        # Validate unit level
        unit_level = expected_structure["unit_level"]
        print(f"   Unit level count: {len(unit_level)}")
        assert len(unit_level) == 14, f"Should have 14 unit entries, got {len(unit_level)}"
        
        # Validate unit numbers are sequential
        unit_numbers = [unit["unit_number"] for unit in unit_level]
        expected_unit_numbers = list(range(1, 15))
        assert unit_numbers == expected_unit_numbers, f"Unit numbers should be 1-14, got {unit_numbers}"
        
        # Validate original unit names preserve duplicates
        original_names = [unit["original_unit_name"] for unit in unit_level]
        expected_names = [
            "Unit 1", "Unit 2", "Unit 3", "Unit 4", "Unit 5", "Unit 6", "Unit 7", "Unit 8", "Unit 9", "Unit 10",
            "Unit 1", "Unit 2", "Unit 3", "Unit 4"  # Duplicates
        ]
        assert original_names == expected_names, f"Original names should preserve duplicates, got {original_names}"
        
        # Validate all have same UID
        all_pks = [
            expected_structure["organization_level"]["pk"],
            expected_structure["plant_level"]["pk"]
        ] + [unit["pk"] for unit in unit_level]
        
        assert all(pk == "ORG_TW_TAIPOWER_12345678" for pk in all_pks), "All levels should have same UID"
        
        print(f"   ✅ Organization level: Correct UID")
        print(f"   ✅ Plant level: Sequential units_id [1-14]")
        print(f"   ✅ Unit level: 14 units with sequential processing")
        print(f"   ✅ Original names: Duplicates preserved for search")
        print(f"   ✅ All levels: Same UID consistency")
        
        return True
        
    except Exception as e:
        print(f"❌ Final JSON structure test failed: {e}")
        return False

def test_search_query_generation():
    """Test search query generation with the new mapping"""
    print("\n🔍 Testing Search Query Generation...")
    
    try:
        # Unit name mapping for Taichung
        unit_mapping = {
            1: 'Unit 1', 2: 'Unit 2', 3: 'Unit 3', 4: 'Unit 4', 5: 'Unit 5',
            6: 'Unit 6', 7: 'Unit 7', 8: 'Unit 8', 9: 'Unit 9', 10: 'Unit 10',
            11: 'Unit 1', 12: 'Unit 2', 13: 'Unit 3', 14: 'Unit 4'
        }
        
        plant_name = "Taichung Power Station"
        
        print(f"🔍 Search Query Examples:")
        
        # Test key units
        test_units = [1, 10, 11, 14]
        for unit_num in test_units:
            original_name = unit_mapping[unit_num]
            search_query = f"{plant_name} {original_name} (Unit {unit_num}) technical specifications"
            print(f"   Unit {unit_num}: '{search_query}'")
        
        # Verify that duplicate units have distinguishable queries
        unit_1_query = f"{plant_name} {unit_mapping[1]} (Unit 1) technical specifications"
        unit_11_query = f"{plant_name} {unit_mapping[11]} (Unit 11) technical specifications"
        
        print(f"\n🔍 Duplicate Unit Distinction:")
        print(f"   First Unit 1:  '{unit_1_query}'")
        print(f"   Second Unit 1: '{unit_11_query}'")
        
        # They should have different (Unit X) parts even though original names are same
        assert "(Unit 1)" in unit_1_query, "First unit should have (Unit 1)"
        assert "(Unit 11)" in unit_11_query, "Second unit should have (Unit 11)"
        assert unit_1_query != unit_11_query, "Queries should be distinguishable"
        
        print(f"   ✅ Duplicate units have distinguishable search queries")
        
        return True
        
    except Exception as e:
        print(f"❌ Search query generation test failed: {e}")
        return False

def main():
    """Run all complete unit mapping tests"""
    print("🧪 COMPLETE UNIT MAPPING FIX TEST")
    print("=" * 60)
    
    tests = [
        test_complete_taichung_mapping,
        test_final_json_structure,
        test_search_query_generation
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
            results.append(False)
    
    print("\n" + "=" * 60)
    print("📊 COMPLETE UNIT MAPPING TEST RESULTS:")
    
    passed = sum(results)
    total = len(results)
    
    for i, (test, result) in enumerate(zip(tests, results)):
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{i+1}. {test.__name__}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 COMPLETE UNIT MAPPING FIX TESTS PASSED!")
        print("\n🚀 Expected results for Taichung Power Station:")
        print("✅ Plant level units_id: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14]")
        print("✅ Unit level processing: 14 units with sequential numbers")
        print("✅ Original unit preservation: Duplicates handled correctly")
        print("✅ Search queries: Distinguishable for duplicate units")
        print("✅ Consistency: Plant and unit levels perfectly aligned")
        return 0
    else:
        print("⚠️ Some complete unit mapping tests failed.")
        return 1

if __name__ == "__main__":
    exit(main())
