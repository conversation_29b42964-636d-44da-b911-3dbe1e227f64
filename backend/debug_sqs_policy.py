#!/usr/bin/env python3
"""
Debug SQS Policy and Access
"""

import os
import json
import boto3
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def check_sqs_policy():
    """Check the current SQS queue policy"""
    print("🔍 Checking SQS Queue Policy...")
    
    try:
        # Initialize SQS client
        sqs_client = boto3.client(
            'sqs',
            aws_access_key_id=os.getenv("AWS_ACCESS_KEY_ID"),
            aws_secret_access_key=os.getenv("AWS_SECRET_ACCESS_KEY"),
            region_name=os.getenv("AWS_REGION", "ap-south-1")
        )
        
        queue_url = os.getenv("SQS_QUEUE_URL")
        print(f"   - Queue URL: {queue_url}")
        
        # Get queue attributes including policy
        response = sqs_client.get_queue_attributes(
            QueueUrl=queue_url,
            AttributeNames=['Policy', 'QueueArn']
        )
        
        attributes = response.get('Attributes', {})
        queue_arn = attributes.get('QueueArn', 'Unknown')
        policy = attributes.get('Policy')
        
        print(f"   - Queue ARN: {queue_arn}")
        
        if policy:
            print("   - Current Policy:")
            policy_dict = json.loads(policy)
            print(json.dumps(policy_dict, indent=2))
            
            # Check if our user is in the policy
            our_user_arn = "arn:aws:iam::910317683845:user/clam_dev-clam.ai"
            found_user = False
            
            for statement in policy_dict.get('Statement', []):
                principal = statement.get('Principal', {})
                if isinstance(principal, dict):
                    aws_principals = principal.get('AWS', [])
                    if isinstance(aws_principals, str):
                        aws_principals = [aws_principals]
                    
                    if our_user_arn in aws_principals:
                        found_user = True
                        print(f"   ✅ Found our user in policy: {our_user_arn}")
                        print(f"   - Actions allowed: {statement.get('Action', [])}")
                        break
            
            if not found_user:
                print(f"   ❌ Our user NOT found in policy: {our_user_arn}")
        else:
            print("   ❌ No policy found on queue")
            
        return True
        
    except Exception as e:
        print(f"   ❌ Error checking policy: {str(e)}")
        return False

def test_sqs_permissions():
    """Test specific SQS permissions"""
    print("\n🧪 Testing SQS Permissions...")
    
    try:
        # Initialize SQS client
        sqs_client = boto3.client(
            'sqs',
            aws_access_key_id=os.getenv("AWS_ACCESS_KEY_ID"),
            aws_secret_access_key=os.getenv("AWS_SECRET_ACCESS_KEY"),
            region_name=os.getenv("AWS_REGION", "ap-south-1")
        )
        
        queue_url = os.getenv("SQS_QUEUE_URL")
        
        # Test 1: Get queue attributes (should work if policy is correct)
        print("   - Testing GetQueueAttributes...")
        try:
            response = sqs_client.get_queue_attributes(
                QueueUrl=queue_url,
                AttributeNames=['QueueArn']
            )
            print("   ✅ GetQueueAttributes: SUCCESS")
        except Exception as e:
            print(f"   ❌ GetQueueAttributes: FAILED - {str(e)}")
        
        # Test 2: Send message (the main operation we need)
        print("   - Testing SendMessage...")
        try:
            test_message = {
                "test": "policy_verification",
                "timestamp": "2024-01-01T00:00:00Z"
            }
            
            response = sqs_client.send_message(
                QueueUrl=queue_url,
                MessageBody=json.dumps(test_message),
                MessageGroupId="test_policy_verification",
                MessageDeduplicationId="test_policy_verification_001"
            )
            print("   ✅ SendMessage: SUCCESS")
            print(f"   - Message ID: {response.get('MessageId', 'unknown')}")
            return True
            
        except Exception as e:
            print(f"   ❌ SendMessage: FAILED - {str(e)}")
            return False
            
    except Exception as e:
        print(f"   ❌ Error testing permissions: {str(e)}")
        return False

def main():
    """Main function"""
    print("🚀 SQS Policy Debug Tool")
    print("=" * 50)
    
    # Check environment variables
    required_vars = ["AWS_ACCESS_KEY_ID", "AWS_SECRET_ACCESS_KEY", "AWS_REGION", "SQS_QUEUE_URL"]
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        print(f"❌ Missing environment variables: {', '.join(missing_vars)}")
        return False
    
    print("✅ Environment variables configured")
    
    # Check policy
    policy_ok = check_sqs_policy()
    
    # Test permissions
    permissions_ok = test_sqs_permissions()
    
    print("\n" + "=" * 50)
    if policy_ok and permissions_ok:
        print("🎉 SQS integration should work!")
    else:
        print("❌ SQS integration has issues")
        print("\nPossible solutions:")
        print("1. Verify the queue policy was updated correctly")
        print("2. Wait a few minutes for AWS policy propagation")
        print("3. Check if the user ARN is exactly correct")
        print("4. Verify the queue is in the correct region")
    
    return policy_ok and permissions_ok

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
