#!/usr/bin/env python3
"""
Test script specifically for organization level pk field fix
"""

import sys
import os
sys.path.append('src')

def test_organization_pk_fix():
    """Test the organization pk field fix logic"""
    print("🔑 Testing Organization PK Field Fix...")
    
    try:
        # Simulate the fixed organization processing logic
        import json
        import hashlib
        import time
        
        def simulate_organization_processing(org_data: dict, state: dict, session_id: str):
            """Simulate the fixed organization processing"""
            
            print(f"[{session_id}] 🔍 DEBUG UID BEFORE PROCESSING:")
            print(f"[{session_id}] 🔍 State keys: {list(state.keys())}")
            print(f"[{session_id}] 🔍 org_uid from state: '{state.get('org_uid', '')}'")
            print(f"[{session_id}] 🔍 Original pk: '{org_data.get('pk', 'NOT_FOUND')}'")

            # CRITICAL FIX: Get/Generate UID FIRST before processing "default null"
            org_uid = state.get("org_uid", "")

            # CRITICAL FIX: If UID is missing, try multiple approaches to get it
            if not org_uid and "organization_name" in org_data and "country_name" in org_data:
                print(f"[{session_id}] 🚨 UID MISSING! Trying multiple approaches...")

                try:
                    # Simulate database lookup
                    def simulate_db_lookup(plant_name: str, country: str):
                        # Simulate known plants in database
                        known_plants = {
                            "Taichung Power Station": {
                                "org_uid": "ORG_TW_TAIPOWER_12345678",
                                "organization_name": "Taiwan Power Company"
                            },
                            "Belledune Power Station": {
                                "org_uid": "ORG_CA_NBPOWER_87654321",
                                "organization_name": "NB Power Corporation"
                            }
                        }
                        return known_plants.get(plant_name)

                    # First, try to find existing UID in database
                    plant_name = "Taichung Power Station"  # Simulate from messages
                    existing_plant = simulate_db_lookup(plant_name, org_data["country_name"])

                    if existing_plant and existing_plant.get("org_uid"):
                        org_uid = existing_plant["org_uid"]
                        print(f"[{session_id}] ✅ Found existing UID in database: {org_uid}")
                    else:
                        # Generate new UID if not found
                        org_hash = hashlib.md5(org_data["organization_name"].encode()).hexdigest()[:6].upper()
                        country_code = org_data["country_name"][:2].upper()
                        timestamp = str(int(time.time()))[-8:]
                        org_uid = f"ORG_{country_code}_{org_hash}_{timestamp}"
                        print(f"[{session_id}] ✅ Generated new UID: {org_uid}")

                    # Update state with the UID
                    state["org_uid"] = org_uid

                except Exception as e:
                    print(f"[{session_id}] ❌ Failed to get/generate UID: {e}")
                    # Last resort: create a simple UID
                    org_hash = hashlib.md5(org_data["organization_name"].encode()).hexdigest()[:6].upper()
                    country_code = org_data["country_name"][:2].upper()
                    timestamp = str(int(time.time()))[-8:]
                    org_uid = f"ORG_{country_code}_{org_hash}_{timestamp}"
                    print(f"[{session_id}] 🚨 Created emergency UID: {org_uid}")
                    state["org_uid"] = org_uid

            # CRITICAL FIX: Set pk field to UID BEFORE processing "default null"
            if org_uid:
                org_data["org_uid"] = org_uid
                org_data["pk"] = org_uid  # Set pk to UID BEFORE replace_default_null
                print(f"[{session_id}] ✅ Set pk field to UID BEFORE null processing: {org_uid}")

            # Process organization data formatting (replace "default null" with null)
            # BUT SKIP the pk field since we already set it correctly
            def replace_default_null(obj):
                if isinstance(obj, dict):
                    for key, value in obj.items():
                        if key == "pk":
                            # SKIP pk field - we already set it correctly
                            continue
                        if value == "default null":
                            obj[key] = None
                        elif isinstance(value, (dict, list)):
                            replace_default_null(value)
                elif isinstance(obj, list):
                    for i, item in enumerate(obj):
                        if item == "default null":
                            obj[i] = None
                        elif isinstance(item, (dict, list)):
                            replace_default_null(item)

            replace_default_null(org_data)
            
            return org_data, org_uid
        
        # Test case 1: Organization with "default null" pk and missing UID in state
        print(f"🔍 Test 1: Organization with 'default null' pk")
        
        org_data = {
            "sk": "scraped#org_details",
            "cfpp_type": "Public",
            "country_name": "Taiwan",
            "currency_in": "TWD",
            "financial_year": "01-12",
            "organization_name": "Taiwan Power Company",
            "plants_count": 12,
            "plant_types": ["Coal", "Nuclear", "Gas"],
            "ppa_flag": "Plant-level",
            "province": "Taiwan",
            "pk": "default null",  # This should be replaced
            "some_other_field": "default null"  # This should become null
        }
        
        state = {
            "session_id": "test_session",
            "messages": [],
            # No org_uid in state - this is the problem scenario
        }
        
        print(f"   Before: pk = '{org_data.get('pk')}'")
        print(f"   Before: org_uid = '{org_data.get('org_uid', 'NOT_SET')}'")
        print(f"   Before: some_other_field = '{org_data.get('some_other_field')}'")
        
        # Apply the fix
        processed_org_data, result_uid = simulate_organization_processing(org_data, state, "test_session")
        
        print(f"   After: pk = '{processed_org_data.get('pk')}'")
        print(f"   After: org_uid = '{processed_org_data.get('org_uid')}'")
        print(f"   After: some_other_field = '{processed_org_data.get('some_other_field')}'")
        
        # Verify the fix worked
        assert processed_org_data["pk"] != "default null", f"pk should not be 'default null', got: {processed_org_data['pk']}"
        assert processed_org_data["pk"].startswith("ORG_"), f"pk should be a valid UID, got: {processed_org_data['pk']}"
        assert processed_org_data["org_uid"] == processed_org_data["pk"], "org_uid and pk should match"
        assert processed_org_data["some_other_field"] is None, "Other 'default null' fields should become None"
        
        print(f"   ✅ Organization pk fix successful!")
        
        # Test case 2: Organization with UID already in state
        print(f"\n🔍 Test 2: Organization with UID already in state")
        
        org_data_2 = {
            "sk": "scraped#org_details",
            "organization_name": "NB Power Corporation",
            "country_name": "Canada",
            "pk": "default null",
        }
        
        state_2 = {
            "session_id": "test_session_2",
            "org_uid": "ORG_CA_NBPOWER_87654321",  # UID already in state
            "messages": []
        }
        
        print(f"   Before: pk = '{org_data_2.get('pk')}'")
        print(f"   State org_uid = '{state_2.get('org_uid')}'")
        
        # Apply the fix
        processed_org_data_2, result_uid_2 = simulate_organization_processing(org_data_2, state_2, "test_session_2")
        
        print(f"   After: pk = '{processed_org_data_2.get('pk')}'")
        print(f"   After: org_uid = '{processed_org_data_2.get('org_uid')}'")
        
        # Verify the fix worked
        assert processed_org_data_2["pk"] == "ORG_CA_NBPOWER_87654321", f"pk should be the state UID, got: {processed_org_data_2['pk']}"
        assert processed_org_data_2["org_uid"] == processed_org_data_2["pk"], "org_uid and pk should match"
        
        print(f"   ✅ State UID preservation successful!")
        
        return True
        
    except Exception as e:
        print(f"❌ Organization pk fix test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_storage_function_integration():
    """Test that the storage function still works correctly"""
    print("\n💾 Testing Storage Function Integration...")
    
    try:
        # Simulate the store_organization_data function behavior
        def simulate_store_organization_data(org_data: dict, plant_name: str, session_id: str, org_uid: str):
            """Simulate the store_organization_data function"""
            
            print(f"[{session_id}] 🔍 DEBUG: org_uid = '{org_uid}'")
            print(f"[{session_id}] 🔍 DEBUG: org_data keys = {list(org_data.keys())}")
            print(f"[{session_id}] 🔍 DEBUG: current pk = '{org_data.get('pk', 'NOT_FOUND')}'")
            
            if org_uid:
                org_data["org_uid"] = org_uid
                # ALWAYS replace pk field with actual UID regardless of current value
                old_pk = org_data.get("pk", "NOT_FOUND")
                org_data["pk"] = org_uid
                print(f"[{session_id}] ✅ Set pk field: '{old_pk}' → '{org_uid}'")
                print(f"[{session_id}] 🔑 Added UID to organization data: {org_uid}")
            else:
                print(f"[{session_id}] ❌ No org_uid provided to store_organization_data")
            
            return f"s3://bucket/plant/organization_level.json"  # Simulate S3 URL
        
        # Test with the processed organization data
        org_data = {
            "organization_name": "Taiwan Power Company",
            "country_name": "Taiwan",
            "pk": "ORG_TW_TAIPOWER_12345678",  # Already set correctly by the fix
            "org_uid": "ORG_TW_TAIPOWER_12345678"
        }
        
        plant_name = "Taichung Power Station"
        session_id = "test_session"
        org_uid = "ORG_TW_TAIPOWER_12345678"
        
        print(f"🔍 Testing storage with correctly processed data:")
        print(f"   Input pk: '{org_data.get('pk')}'")
        print(f"   Input org_uid: '{org_data.get('org_uid')}'")
        print(f"   Parameter org_uid: '{org_uid}'")
        
        # Call storage function
        s3_url = simulate_store_organization_data(org_data, plant_name, session_id, org_uid)
        
        print(f"   Final pk: '{org_data.get('pk')}'")
        print(f"   Final org_uid: '{org_data.get('org_uid')}'")
        print(f"   S3 URL: '{s3_url}'")
        
        # Verify storage worked correctly
        assert org_data["pk"] == org_uid, f"pk should equal org_uid, got pk='{org_data['pk']}', org_uid='{org_uid}'"
        assert org_data["org_uid"] == org_uid, f"org_uid should be set correctly"
        assert s3_url is not None, "S3 URL should be returned"
        
        print(f"   ✅ Storage function integration successful!")
        
        return True
        
    except Exception as e:
        print(f"❌ Storage function integration test failed: {e}")
        return False

def main():
    """Run organization pk fix tests"""
    print("🧪 ORGANIZATION PK FIELD FIX TEST")
    print("=" * 50)
    
    tests = [
        test_organization_pk_fix,
        test_storage_function_integration
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
            results.append(False)
    
    print("\n" + "=" * 50)
    print("📊 ORGANIZATION PK FIX TEST RESULTS:")
    
    passed = sum(results)
    total = len(results)
    
    for i, (test, result) in enumerate(zip(tests, results)):
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{i+1}. {test.__name__}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ORGANIZATION PK FIX TESTS PASSED!")
        print("\n🚀 Expected results:")
        print("✅ Organization pk field: Actual UID (not 'default null')")
        print("✅ UID generation: Works when missing from state")
        print("✅ UID preservation: Works when already in state")
        print("✅ Storage integration: pk field correctly set")
        print("✅ Other fields: 'default null' becomes None as expected")
        return 0
    else:
        print("⚠️ Some organization pk fix tests failed.")
        return 1

if __name__ == "__main__":
    exit(main())
