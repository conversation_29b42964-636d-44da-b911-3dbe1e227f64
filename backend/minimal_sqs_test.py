#!/usr/bin/env python3
"""
Minimal SQS Test - Direct boto3 testing

This script tests SQS functionality directly using boto3 without any dependencies.
"""

import os
import json
import boto3
import hashlib
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_sqs_minimal():
    """Test SQS directly with boto3"""
    print("🔍 Testing SQS with boto3 directly...")
    
    try:
        # Get configuration from environment
        aws_access_key_id = os.getenv("AWS_ACCESS_KEY_ID")
        aws_secret_access_key = os.getenv("AWS_SECRET_ACCESS_KEY")
        aws_region = os.getenv("AWS_REGION", "ap-south-1")
        queue_url = os.getenv("SQS_QUEUE_URL")
        
        print(f"   - Region: {aws_region}")
        print(f"   - Queue URL: {queue_url}")
        
        # Initialize SQS client
        sqs_client = boto3.client(
            'sqs',
            aws_access_key_id=aws_access_key_id,
            aws_secret_access_key=aws_secret_access_key,
            region_name=aws_region
        )
        print("✅ SQS client initialized")
        
        # Skip queue attributes check due to permissions
        print("✅ SQS client ready (skipping attributes check)")
        
        # Create test message
        message_payload = {
            "message_type": "financial_pipeline_trigger",
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "session_id": "minimal_test",
            "data": {
                "org_name": "Adani Power Limited",
                "plant_name": "Mundra Thermal Power Station",
                "country": "India",
                "uid": "test-minimal-12345"
            },
            "metadata": {
                "source": "technical_pipeline",
                "version": "1.0",
                "priority": "normal"
            }
        }
        
        # Generate FIFO requirements
        plant_name = "Mundra Thermal Power Station"
        sanitized_plant = "".join(c.lower() if c.isalnum() else "_" for c in plant_name)
        message_group_id = f"plant_{sanitized_plant}"[:120]
        
        # Generate deduplication ID
        dedup_components = [
            message_payload["data"]["org_name"],
            message_payload["data"]["plant_name"],
            message_payload["data"]["uid"],
            message_payload["session_id"]
        ]
        dedup_string = "|".join(dedup_components)
        message_deduplication_id = hashlib.sha256(dedup_string.encode('utf-8')).hexdigest()
        
        print(f"   - MessageGroupId: {message_group_id}")
        print(f"   - DeduplicationId: {message_deduplication_id[:16]}...")
        
        # Send message to SQS FIFO queue
        response = sqs_client.send_message(
            QueueUrl=queue_url,
            MessageBody=json.dumps(message_payload, indent=2),
            MessageGroupId=message_group_id,
            MessageDeduplicationId=message_deduplication_id,
            MessageAttributes={
                'MessageType': {
                    'StringValue': 'financial_pipeline_trigger',
                    'DataType': 'String'
                },
                'SourceSystem': {
                    'StringValue': 'technical_pipeline',
                    'DataType': 'String'
                },
                'PlantName': {
                    'StringValue': plant_name,
                    'DataType': 'String'
                },
                'OrganizationName': {
                    'StringValue': 'Adani Power Limited',
                    'DataType': 'String'
                }
            }
        )
        
        message_id = response.get('MessageId', 'unknown')
        sequence_number = response.get('SequenceNumber', 'unknown')
        
        print("✅ Message sent successfully!")
        print(f"   - Message ID: {message_id}")
        print(f"   - Sequence Number: {sequence_number}")
        
        return True
        
    except Exception as e:
        print(f"❌ SQS test failed: {str(e)}")
        return False

def main():
    """Run minimal SQS test"""
    print("🚀 Minimal SQS Financial Pipeline Test")
    print("=" * 50)
    
    # Check environment variables
    required_env_vars = [
        "AWS_ACCESS_KEY_ID",
        "AWS_SECRET_ACCESS_KEY", 
        "AWS_REGION",
        "SQS_QUEUE_URL"
    ]
    
    missing_vars = [var for var in required_env_vars if not os.getenv(var)]
    if missing_vars:
        print(f"❌ Missing environment variables: {', '.join(missing_vars)}")
        return False
    
    print("✅ Environment variables configured")
    
    # Run test
    if test_sqs_minimal():
        print("\n🎉 SQS integration is working correctly!")
        print("✅ Financial pipeline can receive messages from technical pipeline")
        return True
    else:
        print("\n❌ SQS integration failed")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)