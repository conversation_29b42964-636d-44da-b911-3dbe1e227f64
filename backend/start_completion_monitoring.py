#!/usr/bin/env python3
"""
Startup script for continuous SQS completion message monitoring

This script can be used to:
1. Start the completion monitoring service as a standalone process
2. Integrate with the main application startup
3. Run as a background daemon
"""

import os
import sys
import time
import argparse
from pathlib import Path

# Add the src directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def start_monitoring_service(check_interval: int = 30, run_forever: bool = True):
    """
    Start the completion monitoring service
    
    Args:
        check_interval: How often to check for messages (seconds)
        run_forever: Whether to run indefinitely or return after starting
    """
    try:
        from agent.completion_monitor_service import start_completion_monitoring, get_monitoring_status
        
        print("🚀 Starting SQS Completion Message Monitoring Service")
        print("=" * 60)
        
        # Start the service
        if start_completion_monitoring(check_interval):
            print("✅ Monitoring service started successfully")
            
            if run_forever:
                print("🔄 Service running continuously...")
                print("   Press Ctrl+C to stop")
                
                try:
                    # Keep the main thread alive
                    while True:
                        time.sleep(60)  # Sleep for 1 minute
                        
                        # Print status every hour
                        status = get_monitoring_status()
                        if status["stats"]["total_checks"] % 120 == 0:  # Every 2 hours worth of checks
                            uptime_hours = status["uptime_seconds"] / 3600 if status["uptime_seconds"] else 0
                            print(f"📊 Service Status: {uptime_hours:.1f}h uptime, "
                                  f"{status['stats']['total_messages_processed']} messages processed")
                
                except KeyboardInterrupt:
                    print("\n🛑 Received shutdown signal")
                    from agent.completion_monitor_service import stop_completion_monitoring
                    stop_completion_monitoring()
                    print("✅ Service stopped gracefully")
            
            return True
        else:
            print("❌ Failed to start monitoring service")
            return False
            
    except Exception as e:
        print(f"❌ Error starting monitoring service: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_sqs_configuration():
    """
    Test SQS configuration before starting monitoring
    
    Returns:
        True if SQS is properly configured, False otherwise
    """
    try:
        from agent.sqs_service import get_sqs_service
        
        print("🔧 Testing SQS Configuration...")
        
        sqs_service = get_sqs_service()
        
        if sqs_service.test_connection("startup_test"):
            print("✅ SQS connection test passed")
            return True
        else:
            print("❌ SQS connection test failed")
            return False
            
    except Exception as e:
        print(f"❌ SQS configuration error: {str(e)}")
        return False

def check_environment_variables():
    """
    Check required environment variables
    
    Returns:
        True if all required variables are set, False otherwise
    """
    required_vars = [
        "SQS_AWS_ACCESS_KEY_ID",
        "SQS_AWS_SECRET_ACCESS_KEY", 
        "SQS_QUEUE_URL"
    ]
    
    print("🔍 Checking environment variables...")
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
        else:
            print(f"   ✅ {var}: {'*' * 8}...")
    
    if missing_vars:
        print("❌ Missing required environment variables:")
        for var in missing_vars:
            print(f"   - {var}")
        return False
    else:
        print("✅ All required environment variables are set")
        return True

def main():
    """
    Main function with command line argument parsing
    """
    parser = argparse.ArgumentParser(
        description="Start SQS Completion Message Monitoring Service",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python start_completion_monitoring.py                    # Start with default settings
  python start_completion_monitoring.py --interval 60     # Check every 60 seconds
  python start_completion_monitoring.py --test-only       # Test configuration only
  python start_completion_monitoring.py --no-daemon       # Start and return (don't run forever)
        """
    )
    
    parser.add_argument(
        "--interval", 
        type=int, 
        default=30,
        help="Check interval in seconds (default: 30)"
    )
    
    parser.add_argument(
        "--test-only",
        action="store_true",
        help="Test configuration only, don't start monitoring"
    )
    
    parser.add_argument(
        "--no-daemon",
        action="store_true", 
        help="Start service and return (don't run as daemon)"
    )
    
    parser.add_argument(
        "--skip-env-check",
        action="store_true",
        help="Skip environment variable validation"
    )
    
    args = parser.parse_args()
    
    print("🚀 SQS Completion Message Monitor Startup")
    print("=" * 50)
    
    # Check environment variables
    if not args.skip_env_check:
        if not check_environment_variables():
            print("\n💡 Fix environment variables and try again")
            return 1
    
    # Test SQS configuration
    if not test_sqs_configuration():
        print("\n💡 Fix SQS configuration and try again")
        return 1
    
    # If test-only mode, exit here
    if args.test_only:
        print("\n✅ Configuration test passed!")
        print("💡 Run without --test-only to start monitoring")
        return 0
    
    # Start monitoring service
    print(f"\n🔄 Starting monitoring with {args.interval}s check interval...")
    
    success = start_monitoring_service(
        check_interval=args.interval,
        run_forever=not args.no_daemon
    )
    
    return 0 if success else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
