#!/usr/bin/env python3
"""
Debug script for <PERSON> power station issues
"""

import os
import sys
import json

# Add the src directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_database_jorge_lacerda():
    """Check <PERSON> plants in database"""
    print("🔍 Checking <PERSON> plants in database...")
    
    try:
        from agent.database_manager import get_database_manager
        
        db_manager = get_database_manager()
        
        # Search for <PERSON> plants
        with db_manager.get_session() as session:
            from agent.database_manager import Plant, Organization
            
            # Find Jorge Lacerda plants
            plants = session.query(Plant).filter(
                Plant.plant_name.like('%<PERSON>')
            ).all()
            
            print(f"📊 Found {len(plants)} Jorge <PERSON> plants:")
            
            for plant in plants:
                print(f"   • {plant.plant_name}")
                print(f"     - UID: {plant.org_uid}")
                print(f"     - Country: {plant.country}")
                print(f"     - Status: {plant.status}")
                print(f"     - Plant ID: {plant.plant_id}")
                
                # Find organization
                org = session.query(Organization).filter(
                    Organization.org_uid == plant.org_uid
                ).first()
                
                if org:
                    print(f"     - Organization: {org.org_name}")
                    print(f"     - Org UID: {org.org_uid}")
                else:
                    print(f"     - Organization: NOT FOUND")
                print()
                
        return True
        
    except Exception as e:
        print(f"❌ Database check failed: {e}")
        return False

def test_uid_generation():
    """Test UID generation for Jorge Lacerda organization"""
    print("\n🔍 Testing UID generation...")
    
    try:
        from agent.database_manager import get_database_manager
        
        db_manager = get_database_manager()
        
        # Test UID generation
        org_name = "Diamante Geração de Energia"
        country = "Brazil"
        
        uid = db_manager.generate_org_uid(org_name, country)
        print(f"✅ Generated UID: {uid}")
        
        # Check if this matches existing UID
        with db_manager.get_session() as session:
            from agent.database_manager import Organization
            
            existing_org = session.query(Organization).filter(
                Organization.org_name == org_name
            ).first()
            
            if existing_org:
                print(f"📊 Existing UID in DB: {existing_org.org_uid}")
                print(f"🔍 Match: {uid == existing_org.org_uid}")
            else:
                print("❌ Organization not found in database")
                
        return True
        
    except Exception as e:
        print(f"❌ UID generation test failed: {e}")
        return False

def test_multi_plant_extraction():
    """Test multi-plant extraction logic"""
    print("\n🔍 Testing multi-plant extraction...")
    
    try:
        from agent.multi_plant_extraction import MultiPlantExtractor
        
        extractor = MultiPlantExtractor()
        
        # Get Jorge Lacerda organization UID
        org_uid = "ORG_BR_DE411A_52205774"  # From database output
        
        plants = extractor.get_organization_plants(org_uid, "test_session")
        
        print(f"📊 Found {len(plants)} plants for org UID {org_uid}:")
        for i, plant in enumerate(plants, 1):
            print(f"   {i}. {plant.get('plant_name', 'Unknown')}")
            print(f"      - Country: {plant.get('country', 'Unknown')}")
            print(f"      - Status: {plant.get('status', 'Unknown')}")
            
        return len(plants) > 0
        
    except Exception as e:
        print(f"❌ Multi-plant extraction test failed: {e}")
        return False

def test_placeholder_uid_issue():
    """Test PLACEHOLDER_UID replacement logic"""
    print("\n🔍 Testing PLACEHOLDER_UID replacement...")
    
    # Simulate organization data with PLACEHOLDER_UID
    org_data = {
        "pk": "PLACEHOLDER_UID",
        "organization_name": "Diamante Geração de Energia",
        "country_name": "Brazil"
    }
    
    org_uid = "ORG_BR_DE411A_52205774"
    
    print(f"Before: pk = {org_data.get('pk')}")
    
    # Apply the fix logic
    if org_uid:
        org_data["org_uid"] = org_uid
        org_data["pk"] = org_uid
        print(f"After: pk = {org_data.get('pk')}")
        print(f"✅ UID replacement successful")
        return True
    else:
        print(f"❌ No org_uid available")
        return False

def main():
    """Run all debug tests"""
    print("🚀 Jorge Lacerda Debug Tests")
    print("=" * 50)
    
    tests = [
        ("Database Check", test_database_jorge_lacerda),
        ("UID Generation", test_uid_generation),
        ("Multi-Plant Extraction", test_multi_plant_extraction),
        ("PLACEHOLDER_UID Fix", test_placeholder_uid_issue)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 Running: {test_name}")
        print("-" * 30)
        
        try:
            if test_func():
                print(f"✅ {test_name} PASSED")
                passed += 1
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} ERROR: {e}")
    
    print("\n" + "=" * 50)
    print(f"🏁 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests PASSED!")
    else:
        print("⚠️ Some tests FAILED. Check the output above.")

if __name__ == "__main__":
    main()
