# SQS Completion Message Monitoring System

## 📋 Overview

This system provides **automatic continuous monitoring** of SQS completion messages from the financial pipeline. It automatically receives, processes, and forwards completion messages to the backend team.

## 🏗️ System Architecture

```
Financial Pipeline → SQS Queue → Completion Monitor → Backend Team Queue
                                      ↓
                               Process & Delete Messages
```

## ✅ What's Implemented

### 1. **SQS Message Receiver** (`sqs_service.py`)
- ✅ Receives completion messages from financial pipeline
- ✅ Filters messages by type (`financial_pipeline_completion`)
- ✅ Handles message parsing and validation
- ✅ Deletes processed messages from queue

### 2. **Completion Handler** (`completion_handler.py`)
- ✅ Processes individual completion messages
- ✅ Sends final completion messages to backend team
- ✅ Tracks processed messages to avoid duplicates
- ✅ Provides batch processing capabilities

### 3. **Continuous Monitor Service** (`completion_monitor_service.py`) **[NEW]**
- ✅ **Runs continuously in background thread**
- ✅ **Automatically polls SQS queue every 30 seconds**
- ✅ **Processes messages automatically when received**
- ✅ **Graceful startup and shutdown**
- ✅ **Status monitoring and statistics**

### 4. **Automatic Startup Integration** (`app.py`) **[NEW]**
- ✅ **Automatically starts monitoring when FastAPI app starts**
- ✅ **Gracefully stops monitoring when app shuts down**
- ✅ **Only starts if SQS is properly configured**

### 5. **Standalone Monitoring Script** (`start_completion_monitoring.py`) **[NEW]**
- ✅ **Can run as standalone background service**
- ✅ **Command-line interface with options**
- ✅ **Configuration testing and validation**

## 🚀 How to Use

### **Option 1: Automatic (Recommended)**
The monitoring service **automatically starts** when you run the FastAPI application:

```bash
cd backend
python -m uvicorn src.agent.app:app --host 0.0.0.0 --port 8000
```

**What happens:**
1. FastAPI app starts
2. System checks if SQS is configured
3. If configured, automatically starts background monitoring
4. Monitoring runs continuously until app shuts down

### **Option 2: Standalone Service**
Run the monitoring service as a standalone background process:

```bash
cd backend
python start_completion_monitoring.py
```

**Options:**
```bash
# Test configuration only
python start_completion_monitoring.py --test-only

# Custom check interval (60 seconds)
python start_completion_monitoring.py --interval 60

# Start and return (don't run as daemon)
python start_completion_monitoring.py --no-daemon
```

### **Option 3: Manual Integration**
Integrate monitoring into your own code:

```python
from agent.completion_monitor_service import start_completion_monitoring, stop_completion_monitoring

# Start monitoring
start_completion_monitoring(check_interval=30)

# Your application code here...

# Stop monitoring
stop_completion_monitoring()
```

## ⚙️ Configuration

### **Required Environment Variables:**
```bash
SQS_AWS_ACCESS_KEY_ID=AKIA5H4ZJQSCTI6WEK4E
SQS_AWS_SECRET_ACCESS_KEY=your_secret_key
SQS_QUEUE_URL=https://sqs.ap-south-1.amazonaws.com/910317683845/transition.fifo
AWS_REGION=ap-south-1
```

### **Optional Configuration:**
- `check_interval`: How often to check for messages (default: 30 seconds)
- `max_messages`: Maximum messages to receive per check (default: 10)

## 🔄 How It Works

### **Continuous Monitoring Flow:**
1. **Background Thread**: Runs continuously in separate thread
2. **Polling**: Checks SQS queue every 30 seconds
3. **Message Processing**: When messages found:
   - Parses and validates completion messages
   - Sends final completion to backend team queue
   - Deletes processed messages from SQS
4. **Error Handling**: Continues running even if individual checks fail
5. **Statistics**: Tracks processed messages and uptime

### **Message Flow:**
```
Financial Pipeline → SQS Message → Monitor Service → Backend Queue
                                        ↓
                                 Delete from SQS
```

## 📊 Monitoring and Status

### **Check Service Status:**
```python
from agent.completion_monitor_service import get_monitoring_status

status = get_monitoring_status()
print(f"Running: {status['running']}")
print(f"Messages processed: {status['stats']['total_messages_processed']}")
print(f"Uptime: {status['uptime_seconds']} seconds")
```

### **Service Statistics:**
- Total checks performed
- Total messages processed
- Service uptime
- Last check time
- Last message processed time
- Error count

## 🧪 Testing

### **Test the Complete System:**
```bash
cd backend
python test_completion_monitoring.py
```

**Tests:**
1. ✅ SQS configuration validation
2. ✅ Integration functions
3. ✅ Auto-start functionality
4. ✅ Monitoring service operation

### **Test Configuration Only:**
```bash
python start_completion_monitoring.py --test-only
```

## 🛠️ Troubleshooting

### **Service Not Starting:**
1. Check environment variables are set
2. Verify SQS queue URL is correct
3. Ensure AWS credentials have SQS permissions
4. Test SQS connection manually

### **No Messages Being Processed:**
1. Verify financial pipeline is sending messages
2. Check message format matches expected structure
3. Ensure messages have correct MessageType attribute
4. Check SQS queue for messages manually

### **Service Stopping Unexpectedly:**
1. Check application logs for errors
2. Verify AWS credentials haven't expired
3. Ensure SQS queue still exists
4. Check network connectivity

## 📝 Integration Examples

### **With FastAPI (Automatic):**
```python
# Already integrated in app.py
# Just start your FastAPI app and monitoring starts automatically
```

### **With Custom Application:**
```python
from agent.completion_monitor_service import auto_start_monitoring_if_configured

# At application startup
if auto_start_monitoring_if_configured():
    print("Monitoring started automatically")
else:
    print("SQS not configured, monitoring not started")
```

### **Manual Control:**
```python
from agent.completion_monitor_service import (
    start_completion_monitoring,
    stop_completion_monitoring,
    get_monitoring_status
)

# Start
start_completion_monitoring(check_interval=60)

# Check status
status = get_monitoring_status()

# Stop
stop_completion_monitoring()
```

## 🎯 Key Benefits

1. **✅ Fully Automatic**: Starts with your application
2. **✅ Continuous Operation**: Runs 24/7 without manual intervention
3. **✅ Reliable**: Handles errors gracefully and continues running
4. **✅ Configurable**: Adjustable check intervals and settings
5. **✅ Monitorable**: Provides status and statistics
6. **✅ Graceful**: Clean startup and shutdown
7. **✅ Standalone**: Can run independently of main application

## 🔗 Related Files

- `src/agent/completion_monitor_service.py` - Main monitoring service
- `src/agent/completion_handler.py` - Message processing logic
- `src/agent/sqs_service.py` - SQS communication
- `src/agent/backend_completion_service.py` - Backend team messaging
- `start_completion_monitoring.py` - Standalone startup script
- `test_completion_monitoring.py` - Test suite

---

**✅ The system now provides fully automatic, continuous monitoring of SQS completion messages as requested!**
