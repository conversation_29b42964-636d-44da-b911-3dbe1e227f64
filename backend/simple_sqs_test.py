#!/usr/bin/env python3
"""
Simple SQS Test - Direct testing without state dependencies

This script tests the SQS service directly to verify the financial pipeline integration.
"""

import os
import sys
from dotenv import load_dotenv

# Add the src directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# Load environment variables
load_dotenv()

def test_sqs_direct():
    """Test SQS service directly"""
    print("🔍 Testing SQS Service Directly...")
    
    try:
        from agent.sqs_service import SQSService
        
        # Initialize service
        sqs_service = SQSService()
        print("✅ SQS service initialized successfully")
        
        # Test connection
        if sqs_service.test_connection("direct_test"):
            print("✅ SQS connection successful")
        else:
            print("❌ SQS connection failed")
            return False
        
        # Test message creation
        payload = sqs_service.create_message_payload(
            org_name="Adani Power Limited",
            plant_name="Mundra Thermal Power Station",
            country="India",
            uid="test-uid-12345",
            session_id="direct_test"
        )
        print("✅ Message payload created successfully")
        
        # Test message sending
        result = sqs_service.send_financial_trigger_message(
            org_name="Adani Power Limited",
            plant_name="Mundra Thermal Power Station",
            country="India",
            uid="test-uid-12345",
            session_id="direct_test"
        )
        
        if result["success"]:
            print("✅ Message sent to SQS successfully!")
            print(f"   - Message ID: {result['message_id']}")
            print(f"   - Sequence Number: {result.get('sequence_number', 'N/A')}")
            print(f"   - Group ID: {result.get('message_group_id', 'N/A')}")
            return True
        else:
            print("❌ Failed to send message to SQS")
            print(f"   - Error: {result.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ SQS test error: {str(e)}")
        return False

def test_convenience_function():
    """Test the convenience function"""
    print("\n🔍 Testing Convenience Function...")
    
    try:
        from agent.sqs_service import send_financial_pipeline_trigger
        
        result = send_financial_pipeline_trigger(
            org_name="Adani Power Limited",
            plant_name="Mundra Thermal Power Station",
            country="India",
            uid="test-convenience-12345",
            session_id="convenience_test"
        )
        
        if result["success"]:
            print("✅ Convenience function test successful!")
            print(f"   - Message ID: {result['message_id']}")
            return True
        else:
            print("❌ Convenience function test failed")
            print(f"   - Error: {result.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ Convenience function test error: {str(e)}")
        return False

def main():
    """Run simple SQS tests"""
    print("🚀 Simple SQS Financial Pipeline Test")
    print("=" * 50)
    
    # Check environment variables
    required_env_vars = [
        "AWS_ACCESS_KEY_ID",
        "AWS_SECRET_ACCESS_KEY", 
        "AWS_REGION",
        "SQS_QUEUE_URL"
    ]
    
    missing_vars = [var for var in required_env_vars if not os.getenv(var)]
    if missing_vars:
        print(f"❌ Missing environment variables: {', '.join(missing_vars)}")
        return False
    
    print("✅ Environment variables configured")
    
    # Run tests
    tests = [
        test_sqs_direct,
        test_convenience_function
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"🏁 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 SQS integration is working!")
        return True
    else:
        print("⚠️ Some tests failed.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)#!/usr/bin/env python3
"""
Simple SQS Test - Direct testing without state dependencies

This script tests the SQS service directly to verify the financial pipeline integration.
"""

import os
import sys
from dotenv import load_dotenv

# Add the src directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# Load environment variables
load_dotenv()

def test_sqs_direct():
    """Test SQS service directly"""
    print("🔍 Testing SQS Service Directly...")
    
    try:
        from agent.sqs_service import SQSService
        
        # Initialize service
        sqs_service = SQSService()
        print("✅ SQS service initialized successfully")
        
        # Test connection
        if sqs_service.test_connection("direct_test"):
            print("✅ SQS connection successful")
        else:
            print("❌ SQS connection failed")
            return False
        
        # Test message creation
        payload = sqs_service.create_message_payload(
            org_name="Adani Power Limited",
            plant_name="Mundra Thermal Power Station",
            country="India",
            uid="test-uid-12345",
            session_id="direct_test"
        )
        print("✅ Message payload created successfully")
        
        # Test message sending
        result = sqs_service.send_financial_trigger_message(
            org_name="Adani Power Limited",
            plant_name="Mundra Thermal Power Station",
            country="India",
            uid="test-uid-12345",
            session_id="direct_test"
        )
        
        if result["success"]:
            print("✅ Message sent to SQS successfully!")
            print(f"   - Message ID: {result['message_id']}")
            print(f"   - Sequence Number: {result.get('sequence_number', 'N/A')}")
            print(f"   - Group ID: {result.get('message_group_id', 'N/A')}")
            return True
        else:
            print("❌ Failed to send message to SQS")
            print(f"   - Error: {result.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ SQS test error: {str(e)}")
        return False

def test_convenience_function():
    """Test the convenience function"""
    print("\n🔍 Testing Convenience Function...")
    
    try:
        from agent.sqs_service import send_financial_pipeline_trigger
        
        result = send_financial_pipeline_trigger(
            org_name="Adani Power Limited",
            plant_name="Mundra Thermal Power Station",
            country="India",
            uid="test-convenience-12345",
            session_id="convenience_test"
        )
        
        if result["success"]:
            print("✅ Convenience function test successful!")
            print(f"   - Message ID: {result['message_id']}")
            return True
        else:
            print("❌ Convenience function test failed")
            print(f"   - Error: {result.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ Convenience function test error: {str(e)}")
        return False

def main():
    """Run simple SQS tests"""
    print("🚀 Simple SQS Financial Pipeline Test")
    print("=" * 50)
    
    # Check environment variables
    required_env_vars = [
        "AWS_ACCESS_KEY_ID",
        "AWS_SECRET_ACCESS_KEY", 
        "AWS_REGION",
        "SQS_QUEUE_URL"
    ]
    
    missing_vars = [var for var in required_env_vars if not os.getenv(var)]
    if missing_vars:
        print(f"❌ Missing environment variables: {', '.join(missing_vars)}")
        return False
    
    print("✅ Environment variables configured")
    
    # Run tests
    tests = [
        test_sqs_direct,
        test_convenience_function
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"🏁 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 SQS integration is working!")
        return True
    else:
        print("⚠️ Some tests failed.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)