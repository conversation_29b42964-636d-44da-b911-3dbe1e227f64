#!/usr/bin/env python3
"""
Test script for UID consistency between organization and plant levels
"""

import sys
import os
sys.path.append('src')

def test_organization_template_placeholder():
    """Test that organization template uses placeholder instead of dummy UID"""
    print("🔑 Testing Organization Template Placeholder...")
    
    try:
        # Read the graph.py file and check for dummy UIDs in templates
        with open('src/agent/graph.py', 'r') as f:
            content = f.read()
        
        # Check for dummy UIDs in organization template
        import re
        
        # Look for the organization template section
        org_template_start = content.find('Example format:')
        org_template_end = content.find('```', org_template_start + 100)
        
        if org_template_start != -1 and org_template_end != -1:
            org_template = content[org_template_start:org_template_end]
            
            # Check for dummy UIDs
            dummy_uids = re.findall(r'"pk":\s*"ORG_[A-Z]{2}_[A-Z0-9]+_[0-9]+"', org_template)
            placeholder_uids = re.findall(r'"pk":\s*"PLACEHOLDER_UID"', org_template)
            
            print(f"🔍 Found {len(dummy_uids)} dummy UIDs in organization template")
            print(f"🔍 Found {len(placeholder_uids)} placeholder UIDs in organization template")
            
            if len(dummy_uids) == 0 and len(placeholder_uids) > 0:
                print(f"   ✅ Organization template uses placeholder correctly")
                return True
            else:
                print(f"   ❌ Organization template still has dummy UIDs or missing placeholder")
                if dummy_uids:
                    print(f"      Dummy UIDs found: {dummy_uids}")
                return False
        else:
            print(f"   ❌ Could not find organization template")
            return False
        
    except Exception as e:
        print(f"❌ Organization template test failed: {e}")
        return False

def test_placeholder_replacement_logic():
    """Test the placeholder replacement logic"""
    print("\n🔄 Testing Placeholder Replacement Logic...")
    
    try:
        # Simulate the placeholder replacement logic
        def simulate_placeholder_replacement(json_str: str, org_uid: str, session_id: str):
            """Simulate the placeholder replacement logic"""
            import json
            
            print(f"[Session {session_id}] 🔍 Processing JSON with potential placeholder...")
            print(f"[Session {session_id}] 🔍 org_uid: {org_uid}")
            
            # Parse initial JSON
            org_data = json.loads(json_str)
            
            # CRITICAL FIX: Set pk field to UID BEFORE processing "default null"
            if org_uid:
                org_data["org_uid"] = org_uid
                org_data["pk"] = org_uid  # Set pk to UID BEFORE replace_default_null
                print(f"[Session {session_id}] ✅ Set pk field to UID BEFORE null processing: {org_uid}")
            
            # CRITICAL FIX: Also replace PLACEHOLDER_UID in the JSON string if present
            if "PLACEHOLDER_UID" in json_str:
                if org_uid:
                    json_str = json_str.replace("PLACEHOLDER_UID", org_uid)
                    org_data = json.loads(json_str)  # Re-parse with actual UID
                    # Make sure org_uid is also set after re-parsing
                    org_data["org_uid"] = org_uid
                    org_data["pk"] = org_uid
                    print(f"[Session {session_id}] ✅ Replaced PLACEHOLDER_UID with actual UID: {org_uid}")
                else:
                    print(f"[Session {session_id}] ⚠️ Found PLACEHOLDER_UID but no org_uid available")

            return org_data
        
        # Test case 1: JSON with PLACEHOLDER_UID
        json_with_placeholder = '''
        {
          "sk": "org_details",
          "organization_name": "Test Power Company",
          "country_name": "India",
          "pk": "PLACEHOLDER_UID",
          "some_field": "default null"
        }
        '''
        
        org_uid = "ORG_IN_179407_52140165"
        
        print(f"🔍 Test 1: JSON with PLACEHOLDER_UID")
        print(f"   Input pk: PLACEHOLDER_UID")
        print(f"   Available org_uid: {org_uid}")
        
        result = simulate_placeholder_replacement(json_with_placeholder, org_uid, "test_session")
        
        print(f"   Output pk: {result.get('pk')}")
        print(f"   Output org_uid: {result.get('org_uid')}")
        
        assert result["pk"] == org_uid, f"pk should be replaced with org_uid, got: {result['pk']}"
        assert result["org_uid"] == org_uid, f"org_uid should be set correctly"
        assert "PLACEHOLDER_UID" not in str(result), "No placeholder should remain in result"
        
        print(f"   ✅ Placeholder replacement successful")
        
        # Test case 2: JSON without placeholder (regular case)
        json_without_placeholder = '''
        {
          "sk": "org_details",
          "organization_name": "Test Power Company",
          "country_name": "India",
          "pk": "default null",
          "some_field": "default null"
        }
        '''
        
        print(f"\n🔍 Test 2: JSON without PLACEHOLDER_UID")
        print(f"   Input pk: default null")
        print(f"   Available org_uid: {org_uid}")
        
        result2 = simulate_placeholder_replacement(json_without_placeholder, org_uid, "test_session")
        
        print(f"   Output pk: {result2.get('pk')}")
        print(f"   Output org_uid: {result2.get('org_uid')}")
        
        assert result2["pk"] == org_uid, f"pk should be set to org_uid, got: {result2['pk']}"
        assert result2["org_uid"] == org_uid, f"org_uid should be set correctly"
        
        print(f"   ✅ Regular case handling successful")
        
        return True
        
    except Exception as e:
        print(f"❌ Placeholder replacement test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_uid_consistency_scenario():
    """Test the complete UID consistency scenario"""
    print("\n🎯 Testing UID Consistency Scenario...")
    
    try:
        # Simulate the complete flow
        def simulate_complete_uid_flow():
            """Simulate the complete UID consistency flow"""
            
            # Step 1: Database lookup/generation (this happens first)
            database_uid = "ORG_IN_179407_52140165"  # This comes from database
            print(f"🔍 Step 1: Database UID: {database_uid}")
            
            # Step 2: Organization level generation (uses template with placeholder)
            org_template_response = '''
            {
              "sk": "org_details",
              "organization_name": "Test Power Company",
              "country_name": "India",
              "pk": "PLACEHOLDER_UID",
              "cfpp_type": "Private"
            }
            '''
            print(f"🔍 Step 2: Organization template response contains: PLACEHOLDER_UID")
            
            # Step 3: Organization processing (replaces placeholder)
            import json
            org_data = json.loads(org_template_response)
            
            # Apply the fix
            if "PLACEHOLDER_UID" in org_template_response:
                org_template_response = org_template_response.replace("PLACEHOLDER_UID", database_uid)
                org_data = json.loads(org_template_response)
                print(f"🔍 Step 3: Replaced placeholder with database UID")
            
            org_data["org_uid"] = database_uid
            org_data["pk"] = database_uid
            
            org_final_uid = org_data["pk"]
            print(f"🔍 Step 3: Organization final pk: {org_final_uid}")
            
            # Step 4: Plant level generation (uses same UID)
            plant_template_response = '''
            {
              "sk": "plant#coal#1",
              "name": "Test Power Station",
              "pk": "PLACEHOLDER_UID",
              "plant_type": "coal"
            }
            '''
            print(f"🔍 Step 4: Plant template response contains: PLACEHOLDER_UID")
            
            # Step 5: Plant processing (uses same UID)
            plant_data = json.loads(plant_template_response)
            
            # Apply the same fix
            if "PLACEHOLDER_UID" in plant_template_response:
                plant_template_response = plant_template_response.replace("PLACEHOLDER_UID", database_uid)
                plant_data = json.loads(plant_template_response)
                print(f"🔍 Step 5: Replaced placeholder with same database UID")
            
            plant_data["pk"] = database_uid
            
            plant_final_uid = plant_data["pk"]
            print(f"🔍 Step 5: Plant final pk: {plant_final_uid}")
            
            # Step 6: Verify consistency
            print(f"🔍 Step 6: Consistency check:")
            print(f"   Database UID: {database_uid}")
            print(f"   Organization pk: {org_final_uid}")
            print(f"   Plant pk: {plant_final_uid}")
            
            consistency_check = (
                database_uid == org_final_uid == plant_final_uid
            )
            
            return consistency_check, database_uid, org_final_uid, plant_final_uid
        
        # Run the simulation
        is_consistent, db_uid, org_uid, plant_uid = simulate_complete_uid_flow()
        
        if is_consistent:
            print(f"   ✅ UID consistency achieved!")
            print(f"   ✅ All levels use the same UID: {db_uid}")
            return True
        else:
            print(f"   ❌ UID inconsistency detected!")
            print(f"   ❌ Database: {db_uid}, Org: {org_uid}, Plant: {plant_uid}")
            return False
        
    except Exception as e:
        print(f"❌ UID consistency test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run UID consistency tests"""
    print("🧪 UID CONSISTENCY TEST")
    print("=" * 50)
    
    tests = [
        test_organization_template_placeholder,
        test_placeholder_replacement_logic,
        test_uid_consistency_scenario
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
            results.append(False)
    
    print("\n" + "=" * 50)
    print("📊 UID CONSISTENCY TEST RESULTS:")
    
    passed = sum(results)
    total = len(results)
    
    for i, (test, result) in enumerate(zip(tests, results)):
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{i+1}. {test.__name__}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 UID CONSISTENCY TESTS PASSED!")
        print("\n🚀 Expected results:")
        print("✅ Organization template: Uses PLACEHOLDER_UID")
        print("✅ Placeholder replacement: Works correctly")
        print("✅ UID consistency: Same UID across all levels")
        print("✅ Database UID = Organization pk = Plant pk")
        return 0
    else:
        print("⚠️ Some UID consistency tests failed.")
        return 1

if __name__ == "__main__":
    exit(main())
