"""
Test Multi-Plant Extraction Implementation

This script tests the complete multi-plant extraction workflow:
1. Database retrieval of organization plants
2. Sequential 3-level extraction for each plant
3. Progress tracking and error handling
"""

import os
import sys

# Add the agent directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_multi_plant_extractor_class():
    """Test the MultiPlantExtractor class functionality"""
    print("\n🔍 Testing MultiPlantExtractor Class")
    print("=" * 50)
    
    try:
        from agent.multi_plant_extraction import MultiPlantExtractor
        
        # Create extractor instance
        extractor = MultiPlantExtractor()
        print("✅ MultiPlantExtractor class imported and instantiated successfully")
        
        # Test with a known organization UID (you can replace with actual UID from your database)
        test_org_uid = "ORG_BR_DE411A_52205774"  # <PERSON> example
        test_session_id = "test_multi_plant"
        
        print(f"\n🔍 Testing plant retrieval for UID: {test_org_uid}")
        
        # Test get_organization_plants method
        plants = extractor.get_organization_plants(test_org_uid, test_session_id)
        
        print(f"📊 Retrieved {len(plants)} plants:")
        for i, plant in enumerate(plants, 1):
            print(f"   {i}. {plant.get('plant_name', 'Unknown')}")
            print(f"      - Country: {plant.get('country', 'Unknown')}")
            print(f"      - Status: {plant.get('status', 'Unknown')}")
            print(f"      - UID: {plant.get('org_uid', 'Unknown')}")
        
        if len(plants) > 1:
            print(f"✅ Multi-plant scenario detected ({len(plants)} plants)")
            return True, plants
        elif len(plants) == 1:
            print(f"ℹ️ Single plant scenario ({len(plants)} plant)")
            return True, plants
        else:
            print(f"⚠️ No plants found for UID {test_org_uid}")
            return False, []
            
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False, []
    except Exception as e:
        print(f"❌ Error testing MultiPlantExtractor: {e}")
        return False, []

def test_database_connection():
    """Test database connection and plant retrieval"""
    print("\n🔍 Testing Database Connection")
    print("=" * 50)
    
    try:
        from agent.database_manager import get_database_manager
        
        db_manager = get_database_manager()
        print("✅ Database manager created successfully")
        
        # Test database connection
        session = db_manager.get_session()
        session.close()
        print("✅ Database connection successful")
        
        # Test retrieving all plants (to see what's in the database)
        session = db_manager.get_session()
        try:
            from agent.database_manager import PowerPlantRegistry
            all_plants = session.query(PowerPlantRegistry).all()
            
            print(f"📊 Total plants in database: {len(all_plants)}")
            
            # Group by organization
            org_groups = {}
            for plant in all_plants:
                org_uid = plant.org_uid
                if org_uid not in org_groups:
                    org_groups[org_uid] = []
                org_groups[org_uid].append(plant)
            
            print(f"📊 Organizations in database: {len(org_groups)}")
            
            for org_uid, plants in org_groups.items():
                org_name = plants[0].org_name if plants else "Unknown"
                print(f"\n🏢 {org_name} (UID: {org_uid})")
                print(f"   Plants: {len(plants)}")
                for plant in plants[:3]:  # Show first 3 plants
                    print(f"   - {plant.plant_name} ({plant.country})")
                if len(plants) > 3:
                    print(f"   ... and {len(plants) - 3} more")
            
            return True, org_groups
            
        finally:
            session.close()
            
    except Exception as e:
        print(f"❌ Database connection error: {e}")
        return False, {}

def test_routing_logic():
    """Test the routing logic for multi-plant extraction"""
    print("\n🔍 Testing Routing Logic")
    print("=" * 50)
    
    try:
        from agent.registry_nodes import route_after_uid_generation
        
        # Test state with multi-plant scenario
        test_state = {
            "session_id": "test_routing",
            "org_uid": "ORG_BR_DE411A_52205774",  # Jorge Lacerda example
            "plant_exists_in_db": True
        }
        
        print(f"🔍 Testing routing with state:")
        print(f"   - Session ID: {test_state['session_id']}")
        print(f"   - Org UID: {test_state['org_uid']}")
        print(f"   - Plant exists: {test_state['plant_exists_in_db']}")
        
        # Test the routing function
        next_node = route_after_uid_generation(test_state)
        
        print(f"📍 Routing result: {next_node}")
        
        if next_node == "run_multi_plant_extraction":
            print("✅ Correctly routed to multi-plant extraction")
            return True
        elif next_node == "trigger_financial_pipeline":
            print("ℹ️ Routed to financial pipeline (single plant)")
            return True
        elif next_node == "populate_database_async":
            print("ℹ️ Routed to database population (new plants)")
            return True
        else:
            print(f"⚠️ Unexpected routing result: {next_node}")
            return False
            
    except Exception as e:
        print(f"❌ Routing logic error: {e}")
        return False

def test_graph_node_existence():
    """Test that the graph node exists"""
    print("\n🔍 Testing Graph Node Existence")
    print("=" * 50)
    
    try:
        from agent.graph import graph
        
        # Check if the node exists in the graph
        graph_nodes = list(graph.nodes.keys())
        
        print(f"📊 Total graph nodes: {len(graph_nodes)}")
        
        if "run_multi_plant_extraction" in graph_nodes:
            print("✅ run_multi_plant_extraction node exists in graph")
            
            # Check related nodes
            related_nodes = [
                "check_plant_registry",
                "quick_org_discovery", 
                "generate_uid",
                "populate_database_async",
                "trigger_financial_pipeline"
            ]
            
            print("\n🔍 Related nodes:")
            for node in related_nodes:
                if node in graph_nodes:
                    print(f"   ✅ {node}")
                else:
                    print(f"   ❌ {node} (missing)")
            
            return True
        else:
            print("❌ run_multi_plant_extraction node NOT found in graph")
            print(f"Available nodes: {graph_nodes}")
            return False
            
    except Exception as e:
        print(f"❌ Graph node test error: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Testing Multi-Plant Extraction Implementation")
    print("=" * 60)
    
    tests = [
        ("Database Connection", test_database_connection),
        ("MultiPlantExtractor Class", test_multi_plant_extractor_class),
        ("Routing Logic", test_routing_logic),
        ("Graph Node Existence", test_graph_node_existence)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            if isinstance(result, tuple):
                success = result[0]
            else:
                success = result
            results.append((test_name, success))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}")
        if success:
            passed += 1
    
    print(f"\n🏁 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED!")
        print("\n💡 Multi-plant extraction is ready to use:")
        print("1. ✅ Database retrieval works")
        print("2. ✅ MultiPlantExtractor class implemented")
        print("3. ✅ Routing logic includes multi-plant path")
        print("4. ✅ Graph node exists and is connected")
        print("\n🚀 You can now run multi-plant extraction!")
    else:
        print(f"\n⚠️ {total - passed} tests failed. Check the issues above.")

if __name__ == "__main__":
    main()
