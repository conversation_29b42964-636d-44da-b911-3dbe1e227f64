#!/usr/bin/env python3
"""
Test Completion Flow

This script tests the complete flow:
1. Receiving completion messages from financial pipeline
2. Sending final completion messages to backend team queue
"""

import sys
import os
import json
import time
from datetime import datetime, timezone

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_backend_completion_service():
    """Test the backend completion service"""
    print("🧪 TESTING BACKEND COMPLETION SERVICE")
    print("=" * 60)
    
    try:
        from agent.backend_completion_service import get_backend_completion_service, send_backend_completion_message
        
        # Test service initialization
        service = get_backend_completion_service()
        print("✅ Backend completion service initialized")
        
        # Test connection
        connection_ok = service.test_connection("test_backend")
        if connection_ok:
            print("✅ Backend queue connection test passed")
        else:
            print("❌ Backend queue connection test failed")
        
        # Test message creation
        message = service.create_completion_message(
            plant_name="Test Power Plant",
            extraction_status="Extraction Completed Successfully",
            session_id="test_backend",
            additional_data={"test_field": "test_value"}
        )
        
        print("✅ Completion message created:")
        print(f"   - Plant: {message['data']['power_plant_name']}")
        print(f"   - Status: {message['data']['status']}")
        print(f"   - Type: {message['message_type']}")
        
        # Test message sending
        result = send_backend_completion_message(
            plant_name="Test Power Plant",
            extraction_status="Extraction Completed Successfully",
            session_id="test_backend",
            additional_data={"organization_name": "Test Organization Ltd"}
        )
        
        if result['success']:
            print("✅ Backend completion message sent successfully")
            print(f"   - Message ID: {result['message_id']}")
            print(f"   - Mode: {result.get('mode', 'unknown')}")
        else:
            print("❌ Backend completion message failed")
            print(f"   - Error: {result.get('error', 'Unknown error')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Backend completion service test failed: {str(e)}")
        return False

def test_sqs_message_receiving():
    """Test SQS message receiving functionality"""
    print("\n🧪 TESTING SQS MESSAGE RECEIVING")
    print("=" * 60)
    
    try:
        from agent.sqs_service import get_sqs_service
        
        # Test service initialization
        sqs_service = get_sqs_service()
        print("✅ SQS service initialized")
        
        # Test connection
        connection_ok = sqs_service.test_connection("test_receive")
        if connection_ok:
            print("✅ SQS connection test passed")
        else:
            print("❌ SQS connection test failed")
            return False
        
        # Test message receiving
        print("🔍 Checking for completion messages...")
        messages = sqs_service.receive_completion_messages("test_receive", max_messages=5)
        
        print(f"📨 Found {len(messages)} completion messages")
        
        for i, message in enumerate(messages, 1):
            print(f"   Message {i}:")
            print(f"     - ID: {message['message_id']}")
            print(f"     - Timestamp: {message['timestamp']}")
            
            # Show message content
            body = message['body']
            if 'data' in body:
                data = body['data']
                print(f"     - Plant: {data.get('plant_name', 'unknown')}")
                print(f"     - Status: {data.get('status', 'unknown')}")
        
        return True
        
    except Exception as e:
        print(f"❌ SQS message receiving test failed: {str(e)}")
        return False

def test_completion_handler():
    """Test the completion handler"""
    print("\n🧪 TESTING COMPLETION HANDLER")
    print("=" * 60)
    
    try:
        from agent.completion_handler import get_completion_handler, process_completion_messages
        
        # Test handler initialization
        handler = get_completion_handler()
        print("✅ Completion handler initialized")
        
        # Test checking for completion messages
        messages = handler.check_for_completion_messages("test_handler")
        print(f"📨 Found {len(messages)} completion messages")
        
        # Test processing all completion messages
        result = process_completion_messages("test_handler")
        
        print("📊 Processing results:")
        print(f"   - Messages found: {result['messages_found']}")
        print(f"   - Messages processed: {result['messages_processed']}")
        print(f"   - Summary: {result['summary']}")
        
        if result['messages_processed'] > 0:
            print("✅ Completion messages processed successfully")
            
            # Show details of processed messages
            for i, msg_result in enumerate(result['results'], 1):
                if msg_result['success']:
                    print(f"   Message {i}: ✅ {msg_result['plant_name']} - {msg_result['extraction_status']}")
                else:
                    print(f"   Message {i}: ❌ Error - {msg_result.get('error', 'Unknown')}")
        else:
            print("ℹ️ No completion messages to process")
        
        return True
        
    except Exception as e:
        print(f"❌ Completion handler test failed: {str(e)}")
        return False

def test_end_to_end_flow():
    """Test the complete end-to-end flow"""
    print("\n🧪 TESTING END-TO-END COMPLETION FLOW")
    print("=" * 60)
    
    try:
        from agent.completion_handler import get_completion_handler
        
        handler = get_completion_handler()
        
        print("🚀 Starting end-to-end completion flow test...")
        
        # Monitor for completion messages for a short duration
        result = handler.monitor_completion_messages(
            session_id="e2e_test",
            duration_seconds=60,  # Monitor for 1 minute
            check_interval=10     # Check every 10 seconds
        )
        
        print("📊 End-to-end test results:")
        print(f"   - Monitoring duration: {result['monitoring_duration']} seconds")
        print(f"   - Total messages processed: {result['total_messages_processed']}")
        print(f"   - Summary: {result['summary']}")
        
        if result['total_messages_processed'] > 0:
            print("✅ End-to-end flow test successful!")
        else:
            print("ℹ️ No messages received during test period")
        
        return True
        
    except Exception as e:
        print(f"❌ End-to-end flow test failed: {str(e)}")
        return False

def simulate_financial_completion_message():
    """Simulate sending a financial completion message for testing"""
    print("\n🧪 SIMULATING FINANCIAL COMPLETION MESSAGE")
    print("=" * 60)
    
    try:
        from agent.sqs_service import get_sqs_service
        import boto3
        import json
        import hashlib
        
        sqs_service = get_sqs_service()
        
        # Create a simulated completion message
        completion_message = {
            "message_type": "financial_pipeline_completion",
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "session_id": "simulation_test",
            "data": {
                "org_name": "Test Organization Ltd",
                "plant_name": "Test Power Plant",
                "country": "India",
                "uid": "ORG_IN_TEST_12345678",
                "status": "completed",
                "financial_analysis_complete": True
            },
            "metadata": {
                "source": "financial_pipeline",
                "version": "1.0",
                "completion_time": datetime.now(timezone.utc).isoformat()
            }
        }
        
        print("📤 Simulated completion message:")
        print(json.dumps(completion_message, indent=2))
        
        # Note: In a real scenario, this would be sent by the financial pipeline
        print("ℹ️ This message would be sent by the financial pipeline")
        print("ℹ️ For testing, we'll just show what the message would look like")
        
        return True
        
    except Exception as e:
        print(f"❌ Simulation failed: {str(e)}")
        return False

def main():
    """Run all completion flow tests"""
    
    print("🚀 COMPLETION FLOW TESTING SUITE")
    print("=" * 80)
    print("Testing the complete flow from financial pipeline completion to backend notification")
    print("=" * 80)
    
    tests = [
        ("Backend Completion Service", test_backend_completion_service),
        ("SQS Message Receiving", test_sqs_message_receiving),
        ("Completion Handler", test_completion_handler),
        ("Financial Message Simulation", simulate_financial_completion_message),
        ("End-to-End Flow", test_end_to_end_flow)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {str(e)}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 80)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 80)
    
    passed = 0
    for test_name, success in results:
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"{status}: {test_name}")
        if success:
            passed += 1
    
    print(f"\n📈 Overall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All tests passed! Completion flow is ready.")
    else:
        print("⚠️ Some tests failed. Review the output above.")
    
    print("\n🔧 IMPLEMENTATION STATUS:")
    print("✅ Task 1: Receiving completion messages from financial pipeline - IMPLEMENTED")
    print("✅ Task 2: Sending final completion messages to backend team - IMPLEMENTED")
    print("📝 Note: Backend team queue is in placeholder mode until queue details are provided")

if __name__ == "__main__":
    main()
