"""
Test Units_ID Processing Fix

This script tests the fix for units_id processing when input is ['Unit 1', 'Unit 2', etc.]
"""

import os
import sys

# Add the agent directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_units_id_processing():
    """Test units_id processing for different input formats"""
    print("\n🔍 Testing Units_ID Processing Fix")
    print("=" * 50)
    
    # Test cases
    test_cases = [
        {
            "name": "String format with Unit prefix",
            "input": ['Unit 1', 'Unit 2', 'Unit 3', 'Unit 4', 'Unit 5'],
            "expected": [1, 2, 3, 4, 5],
            "has_duplicates": False
        },
        {
            "name": "Kosovo A non-sequential",
            "input": [3, 4, 5],
            "expected": [3, 4, 5],
            "has_duplicates": False
        },
        {
            "name": "Taichung with duplicates",
            "input": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 1, 2, 3, 4],
            "expected": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14],
            "has_duplicates": True
        },
        {
            "name": "Mixed string digits",
            "input": ['1', '2', '3'],
            "expected": [1, 2, 3],
            "has_duplicates": False
        },
        {
            "name": "Mixed formats",
            "input": ['Unit 1', 2, '3', 'Unit 4'],
            "expected": [1, 2, 3, 4],
            "has_duplicates": False
        }
    ]
    
    all_passed = True
    
    for case in test_cases:
        print(f"\n🔍 Test case: {case['name']}")
        print(f"   Input: {case['input']}")
        print(f"   Expected: {case['expected']}")
        print(f"   Has duplicates: {case['has_duplicates']}")
        
        # Simulate the processing logic
        units_id = case['input']
        
        # Check if we have duplicates
        has_duplicates = len(units_id) != len(set(units_id))
        
        if has_duplicates:
            # CASE 1: Duplicates found - create sequential mapping
            result = [i+1 for i in range(len(units_id))]
        else:
            # CASE 2: No duplicates - preserve original unit numbers
            clean_units = []
            for unit in units_id:
                if isinstance(unit, int):
                    clean_units.append(unit)
                elif isinstance(unit, str):
                    # Handle both "3" and "Unit 3" formats
                    if unit.isdigit():
                        clean_units.append(int(unit))
                    else:
                        # Extract number from "Unit 1", "Unit 2", etc.
                        import re
                        match = re.search(r'\d+', unit)
                        if match:
                            clean_units.append(int(match.group()))
            
            # Sort to ensure consistent order and remove duplicates
            result = sorted(list(set(clean_units)))
        
        print(f"   Result: {result}")
        
        if result == case['expected']:
            print(f"   ✅ PASS")
        else:
            print(f"   ❌ FAIL")
            all_passed = False
    
    return all_passed

def test_specific_error_case():
    """Test the specific error case from the logs"""
    print("\n🔍 Testing Specific Error Case")
    print("=" * 50)
    
    # This is the exact input that was failing
    input_units = ['Unit 1', 'Unit 2', 'Unit 3', 'Unit 4', 'Unit 5']
    
    print(f"Input: {input_units}")
    
    # Simulate the fixed processing logic
    clean_units = []
    for unit in input_units:
        if isinstance(unit, int):
            clean_units.append(unit)
        elif isinstance(unit, str):
            # Handle both "3" and "Unit 3" formats
            if unit.isdigit():
                clean_units.append(int(unit))
            else:
                # Extract number from "Unit 1", "Unit 2", etc.
                import re
                match = re.search(r'\d+', unit)
                if match:
                    clean_units.append(int(match.group()))
    
    # Sort to ensure consistent order and remove duplicates
    result = sorted(list(set(clean_units)))
    
    print(f"Processed result: {result}")
    
    expected = [1, 2, 3, 4, 5]
    if result == expected:
        print("✅ FIXED: Now correctly extracts [1, 2, 3, 4, 5] from ['Unit 1', 'Unit 2', 'Unit 3', 'Unit 4', 'Unit 5']")
        return True
    else:
        print(f"❌ STILL BROKEN: Expected {expected}, got {result}")
        return False

def main():
    """Run all units_id processing tests"""
    print("🚀 Testing Units_ID Processing Fix")
    print("=" * 60)
    
    tests = [
        ("Units_ID Processing", test_units_id_processing),
        ("Specific Error Case", test_specific_error_case)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 UNITS_ID PROCESSING FIX TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}")
        if success:
            passed += 1
    
    print(f"\n🏁 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 UNITS_ID PROCESSING FIX VERIFIED!")
        print("\n💡 Fixed issue:")
        print("✅ Input: ['Unit 1', 'Unit 2', 'Unit 3', 'Unit 4', 'Unit 5']")
        print("✅ Output: [1, 2, 3, 4, 5]")
        print("✅ No more empty units_id arrays")
        print("✅ Regex extraction from 'Unit X' format works")
        
        print("\n🎯 The error should be resolved now!")
    else:
        print(f"\n⚠️ {total - passed} issues remain")

if __name__ == "__main__":
    main()
