#!/usr/bin/env python3
"""
Completion Integration Script

This script provides easy integration functions for the completion flow
that can be called from your main pipeline.
"""

import sys
import os
import time
from typing import Dict, Any, Optional

# Add src to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def check_and_process_completion_messages(session_id: str = "main_pipeline") -> Dict[str, Any]:
    """
    Check for and process any available completion messages from financial pipeline
    
    Args:
        session_id: Session ID for logging
        
    Returns:
        Dictionary with processing results
    """
    try:
        from agent.completion_handler import process_completion_messages
        
        print(f"[Session {session_id}] 🔍 Checking for financial pipeline completion messages...")
        
        result = process_completion_messages(session_id)
        
        if result['messages_processed'] > 0:
            print(f"[Session {session_id}] ✅ Processed {result['messages_processed']} completion messages")
            
            # Log details of processed messages
            for msg_result in result['results']:
                if msg_result['success']:
                    plant_name = msg_result['plant_name']
                    status = msg_result['extraction_status']
                    print(f"[Session {session_id}] 📤 Sent completion: {plant_name} - {status}")
                else:
                    error = msg_result.get('error', 'Unknown error')
                    print(f"[Session {session_id}] ❌ Failed to process completion: {error}")
        else:
            print(f"[Session {session_id}] ℹ️ No completion messages found")
        
        return result
        
    except Exception as e:
        print(f"[Session {session_id}] ❌ Error checking completion messages: {str(e)}")
        return {
            "success": False,
            "messages_found": 0,
            "messages_processed": 0,
            "error": str(e)
        }

def send_test_backend_completion(plant_name: str, session_id: str = "test") -> Dict[str, Any]:
    """
    Send a test completion message to backend team (for testing purposes)
    
    Args:
        plant_name: Name of the power plant
        session_id: Session ID for logging
        
    Returns:
        Dictionary with send results
    """
    try:
        from agent.backend_completion_service import send_backend_completion_message
        
        print(f"[Session {session_id}] 📤 Sending test completion message for: {plant_name}")
        
        result = send_backend_completion_message(
            plant_name=plant_name,
            extraction_status="Extraction Completed Successfully",
            session_id=session_id,
            additional_data={
                "test_mode": True,
                "organization_name": "Test Organization",
                "organization_uid": "TEST_UID_12345"
            }
        )
        
        if result['success']:
            print(f"[Session {session_id}] ✅ Test completion message sent successfully")
            print(f"[Session {session_id}]   - Message ID: {result['message_id']}")
            print(f"[Session {session_id}]   - Mode: {result.get('mode', 'unknown')}")
        else:
            print(f"[Session {session_id}] ❌ Test completion message failed")
            print(f"[Session {session_id}]   - Error: {result.get('error', 'Unknown error')}")
        
        return result
        
    except Exception as e:
        print(f"[Session {session_id}] ❌ Error sending test completion: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }

def monitor_completion_messages_background(
    duration_minutes: int = 5,
    session_id: str = "background_monitor"
) -> Dict[str, Any]:
    """
    Monitor for completion messages in the background for a specified duration
    
    Args:
        duration_minutes: How long to monitor (default: 5 minutes)
        session_id: Session ID for logging
        
    Returns:
        Dictionary with monitoring results
    """
    try:
        from agent.completion_handler import get_completion_handler
        
        handler = get_completion_handler()
        
        print(f"[Session {session_id}] 👁️ Starting background monitoring for {duration_minutes} minutes...")
        
        result = handler.monitor_completion_messages(
            session_id=session_id,
            duration_seconds=duration_minutes * 60,
            check_interval=30  # Check every 30 seconds
        )
        
        print(f"[Session {session_id}] 🏁 Background monitoring completed")
        print(f"[Session {session_id}] 📊 Total messages processed: {result['total_messages_processed']}")
        
        return result
        
    except Exception as e:
        print(f"[Session {session_id}] ❌ Error in background monitoring: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }

def setup_completion_monitoring():
    """
    Setup completion monitoring (call this once at pipeline startup)
    
    Returns:
        True if setup successful, False otherwise
    """
    try:
        print("🔧 Setting up completion monitoring...")
        
        # Test SQS connection
        from agent.sqs_service import get_sqs_service
        sqs_service = get_sqs_service()
        
        if sqs_service.test_connection("setup"):
            print("✅ SQS connection test passed")
        else:
            print("❌ SQS connection test failed")
            return False
        
        # Test backend completion service
        from agent.backend_completion_service import get_backend_completion_service
        backend_service = get_backend_completion_service()
        
        if backend_service.test_connection("setup"):
            print("✅ Backend completion service ready")
        else:
            print("❌ Backend completion service test failed")
            return False
        
        print("✅ Completion monitoring setup complete")
        return True
        
    except Exception as e:
        print(f"❌ Completion monitoring setup failed: {str(e)}")
        return False

def integration_example():
    """
    Example of how to integrate completion monitoring into your main pipeline
    """
    print("📋 COMPLETION INTEGRATION EXAMPLE")
    print("=" * 60)
    
    print("""
# Example integration in your main pipeline:

def main_pipeline_function():
    # ... your existing pipeline code ...
    
    # 1. Setup completion monitoring (call once at startup)
    if not setup_completion_monitoring():
        print("Warning: Completion monitoring setup failed")
    
    # 2. Check for completion messages periodically
    # (call this every few minutes or after major pipeline steps)
    result = check_and_process_completion_messages("main_pipeline")
    
    if result['messages_processed'] > 0:
        print(f"Processed {result['messages_processed']} completion messages")
    
    # 3. For testing, you can send test completion messages
    # send_test_backend_completion("Test Plant Name", "test_session")
    
    # 4. For continuous monitoring, you can start background monitoring
    # monitor_completion_messages_background(duration_minutes=10)

# Example usage:
if __name__ == "__main__":
    main_pipeline_function()
""")

def main():
    """Main function for testing integration"""
    
    print("🚀 COMPLETION INTEGRATION TEST")
    print("=" * 60)
    
    # Test 1: Setup
    print("\n1️⃣ Testing setup...")
    setup_ok = setup_completion_monitoring()
    
    # Test 2: Check for messages
    print("\n2️⃣ Testing message checking...")
    result = check_and_process_completion_messages("integration_test")
    
    # Test 3: Send test message
    print("\n3️⃣ Testing backend completion...")
    test_result = send_test_backend_completion("Integration Test Plant", "integration_test")
    
    # Test 4: Show integration example
    print("\n4️⃣ Integration example...")
    integration_example()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 INTEGRATION TEST SUMMARY")
    print("=" * 60)
    
    if setup_ok:
        print("✅ Setup: Ready for integration")
    else:
        print("⚠️ Setup: Check environment variables")
    
    print(f"✅ Message checking: Found {result.get('messages_found', 0)} messages")
    
    if test_result.get('success'):
        print("✅ Backend completion: Working (placeholder mode)")
    else:
        print("⚠️ Backend completion: Check configuration")
    
    print("\n🎯 READY FOR INTEGRATION:")
    print("1. Call setup_completion_monitoring() at pipeline startup")
    print("2. Call check_and_process_completion_messages() periodically")
    print("3. Use monitor_completion_messages_background() for continuous monitoring")
    
    print("\n📝 TASKS 1 & 2 COMPLETED:")
    print("✅ Task 1: Receiving completion messages - IMPLEMENTED")
    print("✅ Task 2: Sending backend completion messages - IMPLEMENTED")
    print("🔄 Ready to discuss Task 3: Multi-plant extraction")

if __name__ == "__main__":
    main()
