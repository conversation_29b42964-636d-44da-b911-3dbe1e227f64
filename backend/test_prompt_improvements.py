#!/usr/bin/env python3
"""
Test Prompt Improvements for Plant Sites vs Generating Units
===========================================================

This test verifies that the prompt improvements correctly address the 
Itabo Power Station issue by clearly distinguishing plant sites from units.
"""

def test_prompt_content():
    """Test that prompts contain the correct distinctions"""
    
    print("🧪 TESTING PROMPT IMPROVEMENTS")
    print("=" * 60)
    
    # Test 1: Quick Organization Discovery Prompt
    print("\n1️⃣ Testing Quick Organization Discovery Prompt")
    print("-" * 40)
    
    try:
        with open('src/agent/quick_org_discovery.py', 'r') as f:
            content = f.read()
        
        # Check for key improvements
        improvements = [
            ("POWER PLANT SITES", "Mentions power plant sites specifically"),
            ("not generating units", "Clarifies not counting generating units"),
            ("CRITICAL DISTINCTION", "Has critical distinction section"),
            ("Itabo Power Station", "Uses Itabo as example"),
            ("physical location", "Mentions physical locations"),
            ("distinct power plant facilities", "Mentions distinct facilities")
        ]
        
        found_improvements = []
        for keyword, description in improvements:
            if keyword.lower() in content.lower():
                found_improvements.append((keyword, description))
                print(f"   ✅ {description}")
            else:
                print(f"   ❌ Missing: {description}")
        
        print(f"   📊 Found {len(found_improvements)}/{len(improvements)} improvements")
        
    except Exception as e:
        print(f"   ❌ Error reading quick_org_discovery.py: {e}")
    
    # Test 2: Main Prompts File
    print("\n2️⃣ Testing Main Prompts File")
    print("-" * 40)
    
    try:
        with open('src/agent/prompts.py', 'r') as f:
            content = f.read()
        
        # Check for key improvements
        improvements = [
            ("distinct POWER PLANT SITES", "Mentions distinct power plant sites"),
            ("NOT individual generating units", "Clarifies not counting units"),
            ("CRITICAL: Count only plant sites", "Has critical counting instruction"),
            ("plant sites/facilities", "Mentions sites and facilities"),
            ("Empresa Generadora de Electricidad Itabo", "Uses Itabo example")
        ]
        
        found_improvements = []
        for keyword, description in improvements:
            if keyword.lower() in content.lower():
                found_improvements.append((keyword, description))
                print(f"   ✅ {description}")
            else:
                print(f"   ❌ Missing: {description}")
        
        print(f"   📊 Found {len(found_improvements)}/{len(improvements)} improvements")
        
    except Exception as e:
        print(f"   ❌ Error reading prompts.py: {e}")
    
    # Test 3: Schema Definitions
    print("\n3️⃣ Testing Schema Definitions")
    print("-" * 40)
    
    try:
        with open('src/agent/tools_and_schemas.py', 'r') as f:
            content = f.read()
        
        # Check for key improvements
        improvements = [
            ("distinct POWER PLANT SITES", "Schema mentions distinct plant sites"),
            ("NOT individual generating units", "Schema clarifies not counting units"),
            ("Empresa Generadora de Electricidad Itabo", "Schema uses Itabo example"),
            ("plants_count = 1", "Schema shows correct count example")
        ]
        
        found_improvements = []
        for keyword, description in improvements:
            if keyword.lower() in content.lower():
                found_improvements.append((keyword, description))
                print(f"   ✅ {description}")
            else:
                print(f"   ❌ Missing: {description}")
        
        print(f"   📊 Found {len(found_improvements)}/{len(improvements)} improvements")
        
    except Exception as e:
        print(f"   ❌ Error reading tools_and_schemas.py: {e}")

def test_itabo_scenario():
    """Test the specific Itabo Power Station scenario"""
    
    print("\n🎯 TESTING ITABO POWER STATION SCENARIO")
    print("=" * 60)
    
    print("📋 Scenario Details:")
    print("   Plant: Itabo Power Station")
    print("   Owner: Empresa Generadora de Electricidad Itabo, S.A. (EGE Itabo)")
    print("   Reality: 1 power plant site with multiple generating units")
    print("   Expected plants_count: 1 (not 2 or more)")
    
    print("\n🔍 Key Issues Fixed:")
    print("   ❌ Before: LLM confused generating units with plant sites")
    print("   ❌ Before: Database showed 2 plants instead of 1")
    print("   ✅ After: Clear distinction between sites and units")
    print("   ✅ After: Explicit examples in prompts")
    print("   ✅ After: CRITICAL instructions added")

def test_expected_behavior():
    """Test expected behavior after fixes"""
    
    print("\n🚀 EXPECTED BEHAVIOR AFTER FIXES")
    print("=" * 60)
    
    scenarios = [
        {
            "plant": "Itabo Power Station",
            "owner": "Empresa Generadora de Electricidad Itabo, S.A.",
            "units": ["Unit 1 (150 MW)", "Unit 2 (150 MW)"],
            "expected_plants_count": 1,
            "reason": "Single plant site with multiple generating units"
        },
        {
            "plant": "NTPC Vindhyachal Super Thermal Power Station",
            "owner": "NTPC Limited",
            "units": ["Unit 1", "Unit 2", "Unit 3", "Unit 4", "Unit 5"],
            "expected_plants_count": 1,
            "reason": "Single plant site despite many units"
        },
        {
            "plant": "Adani Mundra Power Station",
            "owner": "Adani Power Limited",
            "units": ["Unit 1", "Unit 2", "Unit 3", "Unit 4", "Unit 5"],
            "expected_plants_count": 1,
            "reason": "Single plant site at Mundra location"
        }
    ]
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"\n{i}️⃣ Scenario: {scenario['plant']}")
        print(f"   Owner: {scenario['owner']}")
        print(f"   Units: {', '.join(scenario['units'])}")
        print(f"   Expected plants_count: {scenario['expected_plants_count']}")
        print(f"   Reason: {scenario['reason']}")
        print(f"   ✅ Should now work correctly with improved prompts")

def main():
    """Run all tests"""
    
    print("🔧 PLANT SITES vs GENERATING UNITS - PROMPT IMPROVEMENTS TEST")
    print("=" * 80)
    print("Verifying fixes for the Itabo Power Station issue")
    print("=" * 80)
    
    # Test prompt content
    test_prompt_content()
    
    # Test Itabo scenario
    test_itabo_scenario()
    
    # Test expected behavior
    test_expected_behavior()
    
    print("\n" + "=" * 80)
    print("📊 SUMMARY OF IMPROVEMENTS")
    print("=" * 80)
    
    print("\n🎯 KEY FIXES IMPLEMENTED:")
    print("1. ✅ Quick Organization Discovery:")
    print("   - Added 'POWER PLANT SITES (not generating units)' in query generation")
    print("   - Added CRITICAL DISTINCTION section in extraction")
    print("   - Added Itabo Power Station as specific example")
    
    print("\n2. ✅ Main Prompts (prompts.py):")
    print("   - Changed 'power plants' to 'distinct POWER PLANT SITES'")
    print("   - Added 'NOT individual generating units' clarification")
    print("   - Added specific Itabo example with plants_count = 1")
    
    print("\n3. ✅ Schema Definitions (tools_and_schemas.py):")
    print("   - Updated plants_count description with clear distinction")
    print("   - Added Itabo example: 'plants_count = 1' despite multiple units")
    
    print("\n4. ✅ Multi-Plant Detection (graph.py):")
    print("   - Updated validation queries to focus on plant sites")
    print("   - Added warnings about not counting generating units")
    
    print("\n🎉 EXPECTED RESULTS:")
    print("✅ Itabo Power Station should now show plants_count = 1")
    print("✅ Database should store 1 plant site, not 2")
    print("✅ LLM will distinguish between plant sites and generating units")
    print("✅ All similar cases should be handled correctly")
    
    print("\n🔄 NEXT STEPS:")
    print("1. Test with a real power plant query")
    print("2. Verify database entries are correct")
    print("3. Check organization-level plants_count field")

if __name__ == "__main__":
    main()
