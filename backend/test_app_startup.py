#!/usr/bin/env python3
"""
Test script to verify the app startup works without lifespan conflicts
"""

import os
import sys

# Add the src directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_app_import():
    """Test that the app can be imported without errors"""
    print("🧪 Testing FastAPI app import...")
    
    try:
        from agent.app import app
        print("✅ FastAPI app imported successfully")
        print(f"   App title: {app.title}")
        print(f"   App version: {app.version}")
        return True
        
    except Exception as e:
        print(f"❌ Error importing app: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_monitoring_init():
    """Test the monitoring initialization module"""
    print("\n🧪 Testing monitoring initialization...")
    
    try:
        from agent.monitoring_init import (
            initialize_completion_monitoring,
            get_monitoring_status,
            is_monitoring_running
        )
        
        print("✅ Monitoring init module imported successfully")
        
        # Test status functions
        status = get_monitoring_status()
        print(f"   Monitoring status: {status}")
        
        running = is_monitoring_running()
        print(f"   Monitoring running: {running}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing monitoring init: {str(e)}")
        return False

def test_manual_monitoring_start():
    """Test manual monitoring start (without SQS config)"""
    print("\n🧪 Testing manual monitoring start...")
    
    try:
        from agent.monitoring_init import start_monitoring_now, cleanup_completion_monitoring
        
        print("   Note: This will fail without SQS config, which is expected")
        
        # Try to start (should fail gracefully without SQS config)
        result = start_monitoring_now(check_interval=60)
        
        if result:
            print("✅ Monitoring started (SQS configured)")
            # Clean up
            cleanup_completion_monitoring()
        else:
            print("ℹ️ Monitoring not started (SQS not configured) - This is expected")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing manual start: {str(e)}")
        return False

def test_environment_check():
    """Test environment variable checking"""
    print("\n🧪 Testing environment variable checking...")
    
    required_vars = [
        "SQS_AWS_ACCESS_KEY_ID",
        "SQS_AWS_SECRET_ACCESS_KEY",
        "SQS_QUEUE_URL"
    ]
    
    configured_vars = 0
    
    for var in required_vars:
        value = os.getenv(var)
        if value:
            print(f"   ✅ {var}: {'*' * 8}...")
            configured_vars += 1
        else:
            print(f"   ❌ {var}: Not set")
    
    if configured_vars == len(required_vars):
        print("✅ All SQS environment variables configured")
        return True
    else:
        print(f"ℹ️ {configured_vars}/{len(required_vars)} SQS environment variables configured")
        print("   Monitoring will not start automatically (this is expected for testing)")
        return True  # This is expected in test environment

def main():
    """Run all tests"""
    print("🚀 FastAPI App Startup Test Suite")
    print("=" * 50)
    
    tests = [
        ("Environment Check", test_environment_check),
        ("FastAPI App Import", test_app_import),
        ("Monitoring Init Module", test_monitoring_init),
        ("Manual Monitoring Start", test_manual_monitoring_start)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 Running: {test_name}")
        print("-" * 30)
        
        try:
            if test_func():
                print(f"✅ {test_name} PASSED")
                passed += 1
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} ERROR: {e}")
    
    print("\n" + "=" * 50)
    print(f"🏁 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED!")
        print("\n📋 APP STARTUP READY:")
        print("1. ✅ FastAPI app imports without errors")
        print("2. ✅ No lifespan conflicts with LangGraph")
        print("3. ✅ Monitoring initialization works")
        print("4. ✅ Manual monitoring control available")
        
        print("\n🚀 HOW TO START:")
        print("1. Set SQS environment variables")
        print("2. Start FastAPI app normally")
        print("3. Monitoring will start automatically if configured")
        print("4. Or use: python start_completion_monitoring.py")
        
    else:
        print("\n⚠️ Some tests failed. Check the output above.")

if __name__ == "__main__":
    main()
