#!/usr/bin/env python3
"""
Test script for the automatic SQS completion message monitoring system
"""

import os
import sys
import time

# Add the src directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_monitoring_service():
    """Test the completion monitoring service"""
    print("🧪 Testing SQS Completion Message Monitoring Service")
    print("=" * 60)
    
    try:
        from agent.completion_monitor_service import (
            start_completion_monitoring,
            stop_completion_monitoring, 
            get_monitoring_status
        )
        
        # Test 1: Start monitoring service
        print("📝 Test 1: Starting monitoring service...")
        if start_completion_monitoring(check_interval=10):  # Check every 10 seconds for testing
            print("✅ Monitoring service started successfully")
        else:
            print("❌ Failed to start monitoring service")
            return False
        
        # Test 2: Check service status
        print("\n📝 Test 2: Checking service status...")
        status = get_monitoring_status()
        print(f"   Running: {status['running']}")
        print(f"   Check interval: {status['check_interval']} seconds")
        print(f"   Thread alive: {status['thread_alive']}")
        
        # Test 3: Let it run for a short time
        print("\n📝 Test 3: Letting service run for 30 seconds...")
        print("   (Service will check for messages every 10 seconds)")
        
        for i in range(6):  # 6 * 5 = 30 seconds
            time.sleep(5)
            status = get_monitoring_status()
            checks = status['stats']['total_checks']
            messages = status['stats']['total_messages_processed']
            print(f"   Status: {checks} checks, {messages} messages processed")
        
        # Test 4: Stop monitoring service
        print("\n📝 Test 4: Stopping monitoring service...")
        if stop_completion_monitoring():
            print("✅ Monitoring service stopped successfully")
        else:
            print("❌ Failed to stop monitoring service")
            return False
        
        print("\n✅ All monitoring service tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Error testing monitoring service: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_auto_start():
    """Test automatic startup functionality"""
    print("\n🧪 Testing Automatic Startup Functionality")
    print("=" * 60)
    
    try:
        from agent.completion_monitor_service import auto_start_monitoring_if_configured
        
        print("📝 Testing auto-start with current configuration...")
        
        if auto_start_monitoring_if_configured():
            print("✅ Auto-start successful (SQS configured)")
            
            # Stop it after testing
            from agent.completion_monitor_service import stop_completion_monitoring
            stop_completion_monitoring()
            
            return True
        else:
            print("ℹ️ Auto-start skipped (SQS not configured)")
            print("   This is expected if SQS environment variables are not set")
            return True
            
    except Exception as e:
        print(f"❌ Error testing auto-start: {str(e)}")
        return False

def test_sqs_configuration():
    """Test SQS configuration"""
    print("\n🧪 Testing SQS Configuration")
    print("=" * 60)
    
    # Check environment variables
    required_vars = [
        "SQS_AWS_ACCESS_KEY_ID",
        "SQS_AWS_SECRET_ACCESS_KEY",
        "SQS_QUEUE_URL"
    ]
    
    print("📝 Checking environment variables...")
    env_configured = True
    
    for var in required_vars:
        value = os.getenv(var)
        if value:
            print(f"   ✅ {var}: {'*' * 8}...")
        else:
            print(f"   ❌ {var}: Not set")
            env_configured = False
    
    if not env_configured:
        print("\n⚠️ SQS environment variables not configured")
        print("   Monitoring service will not start automatically")
        return False
    
    # Test SQS connection
    try:
        from agent.sqs_service import get_sqs_service
        
        print("\n📝 Testing SQS connection...")
        sqs_service = get_sqs_service()
        
        if sqs_service.test_connection("config_test"):
            print("✅ SQS connection test passed")
            return True
        else:
            print("❌ SQS connection test failed")
            return False
            
    except Exception as e:
        print(f"❌ SQS connection error: {str(e)}")
        return False

def test_integration_functions():
    """Test integration functions"""
    print("\n🧪 Testing Integration Functions")
    print("=" * 60)
    
    try:
        # Test completion handler
        print("📝 Testing completion handler...")
        from agent.completion_handler import get_completion_handler
        
        handler = get_completion_handler()
        print("✅ Completion handler initialized")
        
        # Test a quick message check (should return empty if no messages)
        messages = handler.check_for_completion_messages("test_integration")
        print(f"✅ Message check completed: {len(messages)} messages found")
        
        # Test SQS service
        print("\n📝 Testing SQS service...")
        from agent.sqs_service import get_sqs_service
        
        sqs_service = get_sqs_service()
        print("✅ SQS service initialized")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing integration: {str(e)}")
        return False

def main():
    """Run all tests"""
    print("🚀 SQS Completion Message Monitoring Test Suite")
    print("=" * 80)
    
    tests = [
        ("SQS Configuration", test_sqs_configuration),
        ("Integration Functions", test_integration_functions),
        ("Auto-Start Functionality", test_auto_start),
        ("Monitoring Service", test_monitoring_service)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 Running: {test_name}")
        print("-" * 40)
        
        try:
            if test_func():
                print(f"✅ {test_name} PASSED")
                passed += 1
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} ERROR: {e}")
    
    print("\n" + "=" * 80)
    print(f"🏁 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED!")
        print("\n📋 MONITORING SYSTEM READY:")
        print("1. ✅ SQS configuration working")
        print("2. ✅ Monitoring service functional")
        print("3. ✅ Auto-start capability working")
        print("4. ✅ Integration functions ready")
        
        print("\n🚀 HOW TO USE:")
        print("1. Automatic: Monitoring starts when FastAPI app starts")
        print("2. Manual: python start_completion_monitoring.py")
        print("3. Background: Service runs continuously checking every 30s")
        print("4. Processing: Automatically processes completion messages")
        
    else:
        print("\n⚠️ Some tests failed. Check the output above.")
        
        if not test_sqs_configuration():
            print("\n💡 TO FIX SQS CONFIGURATION:")
            print("1. Set SQS_AWS_ACCESS_KEY_ID environment variable")
            print("2. Set SQS_AWS_SECRET_ACCESS_KEY environment variable") 
            print("3. Set SQS_QUEUE_URL environment variable")
            print("4. Ensure AWS credentials have SQS permissions")

if __name__ == "__main__":
    main()
