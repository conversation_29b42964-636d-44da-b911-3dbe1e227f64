#!/usr/bin/env python3
"""
Debug script for Lingan Power Station quick organization discovery
"""

import os
import sys
import json

# Add the src directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_lingan_quick_discovery():
    """Test quick organization discovery for Lingan Power Station"""
    print("🔍 Testing Lingan Power Station Quick Organization Discovery")
    print("=" * 60)
    
    try:
        from agent.quick_org_discovery import QuickOrgDiscovery
        from agent.tools_and_schemas import web_search
        
        # Initialize quick discovery
        discovery = QuickOrgDiscovery()
        
        # Test plant name
        plant_name = "Lingan Power Station"
        print(f"🏭 Plant: {plant_name}")
        
        # Step 1: Test query generation
        print(f"\n📝 Step 1: Generating targeted queries...")
        queries = discovery.generate_quick_queries(plant_name)
        
        print(f"✅ Generated {len(queries)} queries:")
        for i, query in enumerate(queries, 1):
            print(f"   {i}. {query}")
        
        # Step 2: Test search execution (simulate)
        print(f"\n🌐 Step 2: What searches would be executed...")
        print("   (Simulating search results for debugging)")
        
        # Simulate what the search results might look like
        simulated_results = [
            {
                "title": "Lingan Generating Station - Nova Scotia Power",
                "url": "https://www.nspower.ca/about-us/our-facilities/lingan-generating-station",
                "snippet": "Lingan Generating Station is owned and operated by Nova Scotia Power Inc. The facility is located in Sydney, Nova Scotia, Canada."
            },
            {
                "title": "Nova Scotia Power - Power Generation Facilities",
                "url": "https://www.nspower.ca/about-us/our-facilities",
                "snippet": "Nova Scotia Power operates multiple generating stations across Nova Scotia including Lingan, Point Aconi, Tufts Cove, and other facilities."
            },
            {
                "title": "Nova Scotia Power Inc. - Company Profile",
                "url": "https://www.nspower.ca/about-us",
                "snippet": "Nova Scotia Power Inc. is the primary electric utility serving Nova Scotia. The company operates coal, natural gas, hydro, and renewable energy facilities throughout the province."
            }
        ]
        
        # Step 3: Test extraction
        print(f"\n🔍 Step 3: Testing organization extraction...")
        
        # Simulate the context that would be passed to extraction
        context = "\n".join([
            f"Title: {result['title']}\nURL: {result['url']}\nContent: {result['snippet']}\n"
            for result in simulated_results
        ])
        
        print(f"📄 Simulated search context:")
        print(f"   Context length: {len(context)} characters")
        print(f"   Sample: {context[:200]}...")
        
        # Test the extraction logic
        org_info = discovery.extract_organization_info(plant_name, context)
        
        print(f"\n✅ Extraction Results:")
        print(f"   Organization: {org_info.get('org_name', 'NOT_FOUND')}")
        print(f"   Country: {org_info.get('country', 'NOT_FOUND')}")
        print(f"   Plants discovered: {len(org_info.get('plants', []))}")
        
        for i, plant in enumerate(org_info.get('plants', []), 1):
            print(f"      {i}. {plant.get('name', 'Unknown')} ({plant.get('status', 'unknown')})")
        
        # Analysis
        print(f"\n📊 ANALYSIS:")
        if len(org_info.get('plants', [])) == 1:
            print("❌ ISSUE: Only 1 plant discovered")
            print("💡 POSSIBLE CAUSES:")
            print("   1. Search results don't contain information about other plants")
            print("   2. LLM prompt needs improvement to find multiple plants")
            print("   3. Organization actually only owns 1 plant (unlikely for Nova Scotia Power)")
            print("   4. Search queries are too specific to Lingan only")
        else:
            print(f"✅ SUCCESS: {len(org_info.get('plants', []))} plants discovered")
        
        return org_info
        
    except Exception as e:
        print(f"❌ Error in quick discovery test: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_improved_queries():
    """Test improved queries for finding multiple plants"""
    print("\n🔧 Testing Improved Query Generation")
    print("=" * 60)
    
    plant_name = "Lingan Power Station"
    
    # Current queries (from the code)
    current_queries = [
        f'"{plant_name}" owner company organization',
        f'"{plant_name}" location country address',
        f'"{plant_name}" parent company other power plant sites facilities'
    ]
    
    # Improved queries that might find more plants
    improved_queries = [
        f'"Nova Scotia Power" power plants generating stations facilities list',
        f'"Nova Scotia Power Inc" coal natural gas hydro facilities',
        f'Nova Scotia Power generating stations Lingan "Point Aconi" "Tufts Cove"',
        f'"Nova Scotia Power" power generation portfolio all facilities',
        f'Nova Scotia electricity generation facilities utility companies'
    ]
    
    print("📝 Current Queries:")
    for i, query in enumerate(current_queries, 1):
        print(f"   {i}. {query}")
    
    print("\n🚀 Improved Queries:")
    for i, query in enumerate(improved_queries, 1):
        print(f"   {i}. {query}")
    
    print("\n💡 IMPROVEMENTS:")
    print("1. ✅ Include organization name directly (Nova Scotia Power)")
    print("2. ✅ Search for facility lists/portfolios")
    print("3. ✅ Include known plant names to find related facilities")
    print("4. ✅ Use broader terms like 'generating stations' and 'facilities'")
    print("5. ✅ Search for utility company profiles")

def test_extraction_prompt_analysis():
    """Analyze the extraction prompt for potential issues"""
    print("\n🔍 Analyzing Extraction Prompt")
    print("=" * 60)
    
    print("📋 Current Prompt Analysis:")
    print("✅ GOOD ASPECTS:")
    print("   - Clearly distinguishes plant sites vs generating units")
    print("   - Asks for exact legal company names")
    print("   - Requests plant status information")
    print("   - Provides clear JSON format")
    
    print("\n⚠️ POTENTIAL ISSUES:")
    print("   1. Prompt might be too restrictive about 'same parent organization'")
    print("   2. May need to explicitly ask for subsidiary plants")
    print("   3. Could benefit from examples of what constitutes 'other plants'")
    print("   4. Might need to handle utility companies differently")
    
    print("\n💡 SUGGESTED IMPROVEMENTS:")
    print("   1. Add examples of utility company plant portfolios")
    print("   2. Explicitly mention subsidiaries and affiliates")
    print("   3. Ask for 'all power generation facilities' not just 'other plants'")
    print("   4. Include guidance for utility companies vs independent power producers")

def test_nova_scotia_power_knowledge():
    """Test what we know about Nova Scotia Power's facilities"""
    print("\n📚 Nova Scotia Power Knowledge Test")
    print("=" * 60)
    
    print("🏭 Known Nova Scotia Power Facilities:")
    known_facilities = [
        {"name": "Lingan Generating Station", "type": "Coal", "location": "Sydney"},
        {"name": "Point Aconi Generating Station", "type": "Coal", "location": "Point Aconi"},
        {"name": "Tufts Cove Generating Station", "type": "Natural Gas/Oil", "location": "Dartmouth"},
        {"name": "Burnside Generating Station", "type": "Natural Gas", "location": "Dartmouth"},
        {"name": "Trenton Generating Station", "type": "Natural Gas", "location": "Trenton"},
        {"name": "Victoria Junction Generating Station", "type": "Natural Gas", "location": "Bridgetown"},
        {"name": "Wreck Cove Hydro Station", "type": "Hydro", "location": "Cape Breton"},
        {"name": "Annapolis Royal Generating Station", "type": "Tidal", "location": "Annapolis Royal"}
    ]
    
    for i, facility in enumerate(known_facilities, 1):
        print(f"   {i}. {facility['name']} ({facility['type']}) - {facility['location']}")
    
    print(f"\n📊 EXPECTED RESULTS:")
    print(f"   - Organization: Nova Scotia Power Inc.")
    print(f"   - Country: Canada")
    print(f"   - Plants: {len(known_facilities)} major facilities")
    print(f"   - Status: Most operational, some may be retired")
    
    print(f"\n❌ CURRENT ISSUE:")
    print(f"   - Quick discovery only finding 1 plant (Lingan)")
    print(f"   - Should find at least 3-4 major facilities")
    print(f"   - Missing Point Aconi, Tufts Cove, and others")

def main():
    """Run all debug tests"""
    print("🚀 Lingan Power Station Discovery Debug Suite")
    print("=" * 80)
    
    tests = [
        ("Quick Discovery Test", test_lingan_quick_discovery),
        ("Improved Queries", test_improved_queries),
        ("Extraction Prompt Analysis", test_extraction_prompt_analysis),
        ("Nova Scotia Power Knowledge", test_nova_scotia_power_knowledge)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n🧪 Running: {test_name}")
        print("-" * 40)
        
        try:
            result = test_func()
            results[test_name] = result
            print(f"✅ {test_name} completed")
        except Exception as e:
            print(f"❌ {test_name} failed: {e}")
            results[test_name] = None
    
    print("\n" + "=" * 80)
    print("🏁 DEBUG SUMMARY")
    print("=" * 80)
    
    discovery_result = results.get("Quick Discovery Test")
    if discovery_result:
        plants_found = len(discovery_result.get('plants', []))
        print(f"📊 Plants discovered: {plants_found}")
        if plants_found == 1:
            print("❌ CONFIRMED: Only 1 plant found (should be 3-8)")
            print("\n🔧 RECOMMENDED FIXES:")
            print("1. Improve search queries to find organization portfolio")
            print("2. Enhance extraction prompt for utility companies")
            print("3. Add fallback searches for known utility patterns")
            print("4. Test with broader organization-focused queries")
        else:
            print(f"✅ SUCCESS: Multiple plants found")
    else:
        print("❌ Could not test discovery (import/setup issues)")
    
    print("\n💡 NEXT STEPS:")
    print("1. Fix the quick organization discovery queries")
    print("2. Improve the extraction prompt for utility companies")
    print("3. Test with the improved queries")
    print("4. Verify that multi-plant extraction triggers correctly")

if __name__ == "__main__":
    main()
