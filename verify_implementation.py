#!/usr/bin/env python3
"""
Simple verification script to check if our implementation is correct
"""

import os
import re

def check_database_schema():
    """Check if database schema has plant_uid column"""
    print("🔍 Checking database schema...")
    
    try:
        with open('backend/src/agent/database_manager.py', 'r') as f:
            content = f.read()
        
        # Check for plant_uid column
        if 'plant_uid = Column(String(50), nullable=True, index=True)' in content:
            print("✅ plant_uid column added to database schema")
            return True
        else:
            print("❌ plant_uid column not found in database schema")
            return False
    except Exception as e:
        print(f"❌ Error checking database schema: {e}")
        return False

def check_plant_uid_generation():
    """Check if plant UID generation method exists"""
    print("\n🔍 Checking plant UID generation...")
    
    try:
        with open('backend/src/agent/database_manager.py', 'r') as f:
            content = f.read()
        
        # Check for generate_plant_uid method
        if 'def generate_plant_uid(self, plant_name: str, country: str, org_uid: str)' in content:
            print("✅ generate_plant_uid method implemented")
            
            # Check format
            if 'PLT_{country_code}_{plant_hash}_{timestamp}' in content:
                print("✅ Plant UID format is correct")
                return True
            else:
                print("❌ Plant UID format is incorrect")
                return False
        else:
            print("❌ generate_plant_uid method not found")
            return False
    except Exception as e:
        print(f"❌ Error checking plant UID generation: {e}")
        return False

def check_plant_data_formatting():
    """Check if plant data formatting uses plant UID"""
    print("\n🔍 Checking plant data formatting...")
    
    try:
        with open('backend/src/agent/graph.py', 'r') as f:
            content = f.read()
        
        # Check if plant data formatting generates plant UID
        if 'plant_uid = db_manager.generate_plant_uid(plant_name, country, org_uid)' in content:
            print("✅ Plant data formatting generates plant UID")
            
            # Check if pk is set to plant_uid
            if 'plant_data["pk"] = plant_uid' in content:
                print("✅ Plant data pk is set to plant_uid")
                return True
            else:
                print("❌ Plant data pk is not set to plant_uid")
                return False
        else:
            print("❌ Plant data formatting doesn't generate plant UID")
            return False
    except Exception as e:
        print(f"❌ Error checking plant data formatting: {e}")
        return False

def check_unit_data_formatting():
    """Check if unit data formatting uses plant UID"""
    print("\n🔍 Checking unit data formatting...")
    
    try:
        with open('backend/src/agent/graph.py', 'r') as f:
            content = f.read()
        
        # Check if unit data formatting accepts plant_uid parameter
        if 'def process_unit_data_formatting(unit_data: dict, session_id: str, plant_id: int = None, plant_uid: str = None)' in content:
            print("✅ Unit data formatting accepts plant_uid parameter")
            
            # Check if pk is set to plant_uid
            if 'unit_data["pk"] = plant_uid' in content:
                print("✅ Unit data pk is set to plant_uid")
                return True
            else:
                print("❌ Unit data pk is not set to plant_uid")
                return False
        else:
            print("❌ Unit data formatting doesn't accept plant_uid parameter")
            return False
    except Exception as e:
        print(f"❌ Error checking unit data formatting: {e}")
        return False

def check_s3_storage_structure():
    """Check if S3 storage uses new hierarchical structure"""
    print("\n🔍 Checking S3 storage structure...")
    
    try:
        with open('backend/src/agent/json_s3_storage.py', 'r') as f:
            content = f.read()
        
        checks = [
            ('Organization path', 's3_folder = f"{country}/{org_uid_from_db}"'),
            ('Organization filename', 'filename = f"{org_uid_from_db}.json"'),
            ('Plant path', 's3_folder = f"{country}/{org_uid_from_db}/{plant_uid}"'),
            ('Plant filename', 'filename = f"{plant_sk}.json"'),
            ('Unit path', 's3_folder = f"{country}/{org_uid_from_db}/{plant_uid}"'),
            ('Unit filename', 'filename = f"{unit_sk}.json"')
        ]
        
        all_passed = True
        for check_name, check_pattern in checks:
            if check_pattern in content:
                print(f"✅ {check_name} structure implemented")
            else:
                print(f"❌ {check_name} structure not found")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ Error checking S3 storage structure: {e}")
        return False

def check_function_calls_updated():
    """Check if function calls are updated to pass plant_uid"""
    print("\n🔍 Checking function call updates...")
    
    try:
        with open('backend/src/agent/graph.py', 'r') as f:
            content = f.read()
        
        # Count occurrences of updated function calls
        plant_uid_calls = content.count('process_unit_data_formatting(unit_data, session_id, plant_id, plant_uid)')
        plant_uid_calls += content.count('process_unit_data_formatting(unit_technical_data, unit_session_id, plant_id, plant_uid)')
        
        if plant_uid_calls >= 4:  # We updated 4 calls
            print(f"✅ Function calls updated to pass plant_uid ({plant_uid_calls} calls found)")
            return True
        else:
            print(f"❌ Not all function calls updated ({plant_uid_calls} calls found, expected 4+)")
            return False
            
    except Exception as e:
        print(f"❌ Error checking function calls: {e}")
        return False

def main():
    """Run all verification checks"""
    print("🔧 VERIFYING PLANT UID AND S3 STRUCTURE IMPLEMENTATION")
    print("=" * 65)
    
    checks = [
        ("Database Schema", check_database_schema),
        ("Plant UID Generation", check_plant_uid_generation),
        ("Plant Data Formatting", check_plant_data_formatting),
        ("Unit Data Formatting", check_unit_data_formatting),
        ("S3 Storage Structure", check_s3_storage_structure),
        ("Function Call Updates", check_function_calls_updated),
    ]
    
    results = []
    for check_name, check_func in checks:
        try:
            result = check_func()
            results.append((check_name, result))
        except Exception as e:
            print(f"❌ {check_name} failed with exception: {e}")
            results.append((check_name, False))
    
    print("\n" + "=" * 65)
    print("📊 VERIFICATION RESULTS")
    print("=" * 65)
    
    passed = 0
    for check_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {check_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 OVERALL: {passed}/{len(results)} checks passed")
    
    if passed == len(results):
        print("\n🎉 ALL IMPLEMENTATION VERIFIED!")
        print("\n📋 IMPLEMENTATION COMPLETE:")
        print("1. ✅ Database schema updated with plant_uid column and index")
        print("2. ✅ Plant UID generation method implemented")
        print("3. ✅ Plant data processing uses plant_uid as pk")
        print("4. ✅ Unit data processing uses plant_uid as pk")
        print("5. ✅ S3 storage uses new hierarchical structure:")
        print("   📁 Bucket/{Country}/{OrgUID}/{org_uid}.json")
        print("   📁 Bucket/{Country}/{OrgUID}/{PlantUID}/{plant_sk}.json")
        print("   📁 Bucket/{Country}/{OrgUID}/{PlantUID}/{unit_sk}.json")
        print("6. ✅ All function calls updated to pass plant_uid")
        print("\n🚀 READY FOR PRODUCTION TESTING!")
    else:
        print(f"\n⚠️ {len(results) - passed} checks failed. Please review the implementation.")

if __name__ == "__main__":
    main()
