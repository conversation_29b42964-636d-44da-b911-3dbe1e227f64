#!/usr/bin/env python3
"""
Test script to verify data type normalization functionality
"""

import json
import sys
import os

# Add the backend src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend', 'src'))

from agent.fallback_calculations import FallbackCalculationE<PERSON>ine

def test_data_normalization():
    """Test the data normalization with unit_output.json"""
    
    # Load the unit_output.json file
    with open('unit_output.json', 'r') as f:
        unit_data = json.load(f)
    
    # Create unit context from the data
    unit_context = {
        "capacity": unit_data.get("capacity", "590"),
        "technology": unit_data.get("technology", "supercritical"),
        "country": "default"
    }
    
    print("🔧 TESTING DATA TYPE NORMALIZATION")
    print("=" * 50)
    
    # Show original data types
    print("\n📊 ORIGINAL DATA TYPES:")
    print(f"PLF values: {type(unit_data['plf'][0]['value'])} - {unit_data['plf'][0]['value']}")
    print(f"Auxiliary Power: {type(unit_data['auxiliary_power_consumed'][0]['value'])} - {unit_data['auxiliary_power_consumed'][0]['value']}")
    print(f"PAF values: {type(unit_data['PAF'][0]['value'])} - {unit_data['PAF'][0]['value']}")
    print(f"Generation: {type(unit_data['gross_power_generation'][0]['value'])} - {unit_data['gross_power_generation'][0]['value']}")
    print(f"Emission Factor: {type(unit_data['emission_factor'][0]['value'])} - {unit_data['emission_factor'][0]['value']}")
    print(f"Coal Efficiency: {type(unit_data['coal_unit_efficiency'])} - {unit_data['coal_unit_efficiency']}")
    print(f"GCV Coal: {type(unit_data['gcv_coal'])} - {unit_data['gcv_coal']}")
    
    # Initialize the fallback engine
    engine = FallbackCalculationEngine()
    
    # Apply normalization
    normalized_data = engine.enhance_unit_data(unit_data, unit_context, "test-session")
    
    print("\n📊 NORMALIZED DATA TYPES:")
    print(f"PLF values: {type(normalized_data['plf'][0]['value'])} - {normalized_data['plf'][0]['value']}")
    print(f"Auxiliary Power: {type(normalized_data['auxiliary_power_consumed'][0]['value'])} - {normalized_data['auxiliary_power_consumed'][0]['value']}")
    print(f"PAF values: {type(normalized_data['PAF'][0]['value'])} - {normalized_data['PAF'][0]['value']}")
    print(f"Generation: {type(normalized_data['gross_power_generation'][0]['value'])} - {normalized_data['gross_power_generation'][0]['value']}")
    print(f"Emission Factor: {type(normalized_data['emission_factor'][0]['value'])} - {normalized_data['emission_factor'][0]['value']}")
    print(f"Coal Efficiency: {type(normalized_data['coal_unit_efficiency'])} - {normalized_data['coal_unit_efficiency']}")
    print(f"GCV Coal: {type(normalized_data['gcv_coal'])} - {normalized_data['gcv_coal']}")
    
    # Show fuel years percentage normalization
    print(f"\nFuel Years Percentage (Coal 2024): {type(normalized_data['fuel_type'][0]['years_percentage']['2024'])} - {normalized_data['fuel_type'][0]['years_percentage']['2024']}")
    
    # Save normalized data for comparison
    with open('unit_output_normalized.json', 'w') as f:
        json.dump(normalized_data, f, indent=2)
    
    print("\n✅ NORMALIZATION TEST COMPLETED")
    print("📁 Normalized data saved to: unit_output_normalized.json")

if __name__ == "__main__":
    test_data_normalization()