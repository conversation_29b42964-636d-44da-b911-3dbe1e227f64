#!/usr/bin/env python3
"""
Comprehensive test for PLF fix - simulates the exact issue you reported
"""

def simulate_plf_fix():
    """Simulate the PLF fix logic without importing the full module"""
    
    def extract_numeric_value(value):
        """Extract numeric value from string"""
        if isinstance(value, (int, float)):
            return float(value)
        if isinstance(value, str):
            cleaned = value.replace(",", "").replace("MW", "").replace("GW", "").replace("%", "").strip()
            try:
                return float(cleaned)
            except:
                return 0.0
        return 0.0
    
    def extract_generation_with_unit_conversion(value_str):
        """Extract generation value and convert to MWh if needed"""
        if not isinstance(value_str, str):
            try:
                return float(value_str)
            except:
                return 0.0
        
        value_lower = value_str.lower()
        numeric_value = extract_numeric_value(value_str)
        
        if numeric_value <= 0:
            return 0.0
        
        # Convert based on detected units
        if any(unit in value_lower for unit in ['gwh', 'gw-h', 'gw h']):
            return numeric_value * 1000  # GWh to MWh
        elif any(unit in value_lower for unit in ['twh', 'tw-h', 'tw h']):
            return numeric_value * 1000000  # TWh to MWh
        elif any(unit in value_lower for unit in ['kwh', 'kw-h', 'kw h']):
            return numeric_value / 1000  # kWh to MWh
        else:
            return numeric_value
    
    def calculate_plf(annual_generation_mwh, capacity_mw):
        """Calculate PLF"""
        max_generation_mwh = capacity_mw * 8760
        plf = (annual_generation_mwh / max_generation_mwh) * 100
        return round(plf, 2)
    
    def fix_unrealistic_plf_values(plf_data, generation_data, capacity):
        """Fix unrealistic PLF values"""
        # Create generation lookup by year
        generation_by_year = {}
        for gen_record in generation_data:
            if isinstance(gen_record, dict) and "value" in gen_record and "year" in gen_record:
                year = gen_record["year"]
                generation_mwh = extract_generation_with_unit_conversion(gen_record["value"])
                if generation_mwh > 0:
                    generation_by_year[year] = generation_mwh
        
        fixed_plf_data = []
        for plf_item in plf_data:
            if isinstance(plf_item, dict) and "value" in plf_item:
                plf_val = extract_numeric_value(plf_item["value"])
                year = plf_item.get("year", "Unknown")
                
                # If PLF is unrealistic and we have generation data for this year
                if (plf_val > 100 or plf_val < 0) and year in generation_by_year:
                    generation_mwh = generation_by_year[year]
                    
                    # Calculate correct PLF
                    corrected_plf = calculate_plf(generation_mwh, capacity)
                    
                    # Apply unit detection if still unrealistic
                    if corrected_plf > 100:
                        # Try GWh to MWh conversion
                        corrected_generation = generation_mwh / 1000
                        corrected_plf = calculate_plf(corrected_generation, capacity)
                        
                        if 10 <= corrected_plf <= 100:
                            print(f"🔧 Fixed PLF for {year}: {plf_val}% → {corrected_plf:.1f}% (GWh conversion)")
                        else:
                            # Try TWh to MWh conversion
                            corrected_generation = generation_mwh / 1000000
                            corrected_plf = calculate_plf(corrected_generation, capacity)
                            
                            if 10 <= corrected_plf <= 100:
                                print(f"🔧 Fixed PLF for {year}: {plf_val}% → {corrected_plf:.1f}% (TWh conversion)")
                            else:
                                print(f"⚠️ Could not fix PLF for {year}: {plf_val}%")
                                corrected_plf = plf_val  # Keep original if can't fix
                    else:
                        print(f"🔧 Fixed PLF for {year}: {plf_val}% → {corrected_plf:.1f}%")
                    
                    # Add corrected PLF
                    fixed_plf_data.append({
                        "value": f"{corrected_plf:.1f}%",
                        "year": year,
                        "_corrected": True,
                        "_original_value": plf_item["value"]
                    })
                else:
                    # Keep original if reasonable or no generation data
                    fixed_plf_data.append(plf_item)
            else:
                fixed_plf_data.append(plf_item)
        
        return fixed_plf_data
    
    print("🧪 COMPREHENSIVE PLF FIX TEST")
    print("=" * 60)
    
    # Test Case 1: Your exact problematic data
    print("\n📋 Test Case 1: Your Exact Problematic PLF Data")
    print("-" * 40)
    
    # Simulate your problematic PLF data
    problematic_plf_data = [
        {"value": "214.48%", "year": "Annual"},
        {"value": "156.01%", "year": "2024"},
        {"value": "179.69%", "year": "2023"},
        {"value": "188.95%", "year": "2022"},
        {"value": "194.51%", "year": "2021"},
        {"value": "181.54%", "year": "2020"}
    ]
    
    # Simulate corresponding generation data (reverse-calculated from PLF)
    capacity = 660  # MW (assumed)
    max_generation = capacity * 8760  # MWh per year
    
    # Calculate what generation values would produce these PLF values
    generation_data = []
    for plf_item in problematic_plf_data:
        if plf_item["year"] != "Annual":  # Skip Annual for this test
            plf_val = extract_numeric_value(plf_item["value"])
            implied_generation = (plf_val / 100) * max_generation
            generation_data.append({
                "value": str(int(implied_generation)),
                "year": plf_item["year"]
            })
    
    print(f"Capacity: {capacity} MW")
    print("Problematic PLF data:")
    for item in problematic_plf_data:
        print(f"   {item['value']} ({item['year']})")
    
    print("Implied generation data:")
    for item in generation_data:
        print(f"   {item['value']} MWh ({item['year']})")
    
    # Apply the fix
    fixed_plf_data = fix_unrealistic_plf_values(problematic_plf_data, generation_data, capacity)
    
    print("\nResults:")
    for original, fixed in zip(problematic_plf_data, fixed_plf_data):
        if "_corrected" in fixed:
            print(f"   {original['year']}: {original['value']} → {fixed['value']} ✅")
        else:
            print(f"   {original['year']}: {original['value']} (unchanged)")
    
    # Test Case 2: Second set of problematic data
    print("\n📋 Test Case 2: Second Set of Problematic PLF Data")
    print("-" * 40)
    
    problematic_plf_data_2 = [
        {"value": "129.69%", "year": "Annual"},
        {"value": "94.34%", "year": "2024"},
        {"value": "108.65%", "year": "2023"},
        {"value": "114.26%", "year": "2022"},
        {"value": "117.62%", "year": "2021"},
        {"value": "109.77%", "year": "2020"}
    ]
    
    # Calculate corresponding generation data
    generation_data_2 = []
    for plf_item in problematic_plf_data_2:
        if plf_item["year"] != "Annual":
            plf_val = extract_numeric_value(plf_item["value"])
            implied_generation = (plf_val / 100) * max_generation
            generation_data_2.append({
                "value": str(int(implied_generation)),
                "year": plf_item["year"]
            })
    
    print("Problematic PLF data:")
    for item in problematic_plf_data_2:
        print(f"   {item['value']} ({item['year']})")
    
    # Apply the fix
    fixed_plf_data_2 = fix_unrealistic_plf_values(problematic_plf_data_2, generation_data_2, capacity)
    
    print("\nResults:")
    for original, fixed in zip(problematic_plf_data_2, fixed_plf_data_2):
        if "_corrected" in fixed:
            print(f"   {original['year']}: {original['value']} → {fixed['value']} ✅")
        else:
            print(f"   {original['year']}: {original['value']} (unchanged)")
    
    print("\n" + "=" * 60)
    print("🎯 SUMMARY:")
    print("The fix detects unrealistic PLF values (>100%) and:")
    print("1. Finds corresponding generation data for the same year")
    print("2. Recalculates PLF using correct formula")
    print("3. Applies unit conversion if PLF is still >100%")
    print("4. Only accepts results in reasonable range (10-100%)")
    
    # Count successful fixes
    total_fixes_1 = sum(1 for item in fixed_plf_data if "_corrected" in item)
    total_fixes_2 = sum(1 for item in fixed_plf_data_2 if "_corrected" in item)
    
    print(f"\n✅ Fixed {total_fixes_1}/{len(problematic_plf_data)} values in Test Case 1")
    print(f"✅ Fixed {total_fixes_2}/{len(problematic_plf_data_2)} values in Test Case 2")

if __name__ == "__main__":
    simulate_plf_fix()