#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to fix the indentation issues in the enhanced_unit_detection function
"""

def fix_indentation():
    """Fix the indentation issues in graph.py"""
    
    # Read the file
    with open('backend/src/agent/graph.py', 'r') as f:
        lines = f.readlines()
    
    # Find the enhanced_unit_detection function
    start_line = None
    try_line = None
    except_line = None
    
    for i, line in enumerate(lines):
        if 'def enhanced_unit_detection(' in line:
            start_line = i
            print(f"Found function start at line {i+1}")
        elif start_line is not None and line.strip() == 'try:':
            try_line = i
            print(f"Found try block at line {i+1}")
        elif start_line is not None and 'except Exception as e:' in line and 'enhanced_unit_detection' in lines[i+1]:
            except_line = i
            print(f"Found except block at line {i+1}")
            break
    
    if start_line is None or try_line is None or except_line is None:
        print("Could not find function boundaries")
        return False
    
    print(f"Function boundaries: start={start_line+1}, try={try_line+1}, except={except_line+1}")
    
    # Fix indentation for lines between try and except
    fixed_lines = lines.copy()
    
    for i in range(try_line + 1, except_line):
        line = lines[i]
        
        # Skip empty lines
        if line.strip() == '':
            continue
            
        # Skip lines that are already properly indented (start with 8+ spaces)
        if line.startswith('        '):
            continue
            
        # Skip lines that are comments or strings that should not be indented
        stripped = line.strip()
        if stripped.startswith('#') or stripped.startswith('"""') or stripped.startswith("'''"):
            continue
        
        # Fix indentation: if line starts with 4 spaces or less, add 4 more spaces
        if line.startswith('    ') and not line.startswith('        '):
            fixed_lines[i] = '    ' + line
            print(f"Fixed line {i+1}: {line.strip()}")
        elif not line.startswith(' '):
            # Line has no indentation, add 8 spaces
            fixed_lines[i] = '        ' + line
            print(f"Fixed line {i+1}: {line.strip()}")
    
    # Write the fixed file
    with open('backend/src/agent/graph.py', 'w') as f:
        f.writelines(fixed_lines)
    
    print("Indentation fixed!")
    return True

def check_syntax():
    """Check if the syntax is now correct"""
    import subprocess
    
    try:
        result = subprocess.run(['python3', '-m', 'py_compile', 'backend/src/agent/graph.py'], 
                              capture_output=True, text=True, cwd='/Users/<USER>/Desktop/Final Phase')
        
        if result.returncode == 0:
            print("✅ Syntax is now correct!")
            return True
        else:
            print(f"❌ Syntax error still exists:")
            print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ Error checking syntax: {e}")
        return False

def main():
    """Main function"""
    print("🔧 FIXING INDENTATION ISSUES IN GRAPH.PY")
    print("=" * 50)
    
    if fix_indentation():
        print("\n🔍 Checking syntax...")
        if check_syntax():
            print("\n🎉 ALL INDENTATION ISSUES FIXED!")
        else:
            print("\n⚠️ Some issues may remain")
    else:
        print("\n❌ Failed to fix indentation")

if __name__ == "__main__":
    main()
