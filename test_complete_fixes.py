"""
Complete Fixes Test

This script tests all the fixes for:
1. AttributeError: 'NoneType' object has no attribute 'query'
2. off_peak_hours and peak_hours fixed values
3. Agent workflow progression from org to plant level
"""

import os
import sys
import json

# Add the agent directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend', 'src'))

def test_quick_org_discovery_fix():
    """Test the AttributeError fix in quick_org_discovery.py"""
    print("\n🔍 Testing Quick Org Discovery Fix")
    print("=" * 50)
    
    try:
        from agent.quick_org_discovery import QuickOrgDiscovery
        
        # Create instance
        discovery = QuickOrgDiscovery()
        
        # Test with a real plant name
        plant_name = "Brikel power station"
        
        print(f"Testing quick discovery for: {plant_name}")
        
        # This should not crash with AttributeError anymore
        queries = discovery.generate_quick_queries(plant_name)
        
        print(f"✅ Generated {len(queries)} queries successfully")
        for i, query in enumerate(queries[:3], 1):
            print(f"   {i}. {query}")
        
        return True
        
    except Exception as e:
        print(f"❌ Quick org discovery test failed: {e}")
        return False

def test_organization_fixed_values():
    """Test that off_peak_hours and peak_hours are correctly set"""
    print("\n🔍 Testing Organization Fixed Values")
    print("=" * 50)
    
    try:
        # Check the graph.py file for the fixed values
        with open('backend/src/agent/graph.py', 'r') as f:
            content = f.read()
        
        # Check if the fixed values are set AFTER null replacement
        if 'org_data["off_peak_hours"] = 0.466' in content and 'org_data["peak_hours"] = 0.9' in content:
            print("✅ Fixed values found in organization processing")
            
            # Check if they're set after null replacement
            if 'Set fixed values AFTER null replacement' in content:
                print("✅ Fixed values are set AFTER null replacement (correct order)")
                return True
            else:
                print("⚠️ Fixed values might be overridden by null replacement")
                return False
        else:
            print("❌ Fixed values not found in organization processing")
            return False
            
    except Exception as e:
        print(f"❌ Organization fixed values test failed: {e}")
        return False

def test_workflow_progression():
    """Test that the workflow progresses from org to plant level"""
    print("\n🔍 Testing Workflow Progression")
    print("=" * 50)
    
    try:
        # Check the graph.py file for correct routing
        with open('backend/src/agent/graph.py', 'r') as f:
            content = f.read()
        
        # Check for correct routing from org to plant level
        routing_checks = [
            'builder.add_edge("org_finalize_answer", "clear_state_for_plant_level")',
            'builder.add_edge("clear_state_for_plant_level", "plant_generate_query")',
            'def clear_state_for_plant_level(state: OverallState)',
            '"search_phase": 2,  # Set to plant-level phase'
        ]
        
        passed_checks = 0
        for check in routing_checks:
            if check in content:
                passed_checks += 1
                print(f"✅ Found: {check[:50]}...")
            else:
                print(f"❌ Missing: {check[:50]}...")
        
        if passed_checks == len(routing_checks):
            print(f"✅ All {len(routing_checks)} workflow routing checks passed")
            return True
        else:
            print(f"❌ Only {passed_checks}/{len(routing_checks)} workflow routing checks passed")
            return False
            
    except Exception as e:
        print(f"❌ Workflow progression test failed: {e}")
        return False

def test_enhanced_search_queries():
    """Test that enhanced search queries are implemented"""
    print("\n🔍 Testing Enhanced Search Queries")
    print("=" * 50)
    
    try:
        # Check for closure_year and mandatory_closure searches
        with open('backend/src/agent/graph.py', 'r') as f:
            content = f.read()
        
        closure_checks = [
            'closure_year and mandatory_closure',
            'scheduled or officially announced closure year',
            'mandatory closure requirement',
            'plant_data["closure_year"]',
            'plant_data["mandatory_closure"]'
        ]
        
        passed_checks = 0
        for check in closure_checks:
            if check in content:
                passed_checks += 1
                print(f"✅ Found closure search: {check[:40]}...")
            else:
                print(f"❌ Missing closure search: {check[:40]}...")
        
        if passed_checks >= 3:
            print(f"✅ Closure search queries implemented ({passed_checks}/{len(closure_checks)} checks)")
            return True
        else:
            print(f"❌ Closure search queries incomplete ({passed_checks}/{len(closure_checks)} checks)")
            return False
            
    except Exception as e:
        print(f"❌ Enhanced search queries test failed: {e}")
        return False

def test_level_4_transition_plan():
    """Test that Level-4 transition plan is implemented"""
    print("\n🔍 Testing Level-4 Transition Plan")
    print("=" * 50)
    
    try:
        # Check json_s3_storage.py for transition plan function
        with open('backend/src/agent/json_s3_storage.py', 'r') as f:
            content = f.read()
        
        transition_checks = [
            'def store_transition_plan_data',
            '"pk": org_uid',
            '"sk": "transition_plan"',
            '"selected_plan_id": ""',
            '"transitionPlanStratName": ""'
        ]
        
        passed_checks = 0
        for check in transition_checks:
            if check in content:
                passed_checks += 1
                print(f"✅ Found transition plan: {check[:40]}...")
            else:
                print(f"❌ Missing transition plan: {check[:40]}...")
        
        # Check if it's called in the workflow
        with open('backend/src/agent/graph.py', 'r') as f:
            graph_content = f.read()
        
        if 'store_transition_plan_data' in graph_content:
            passed_checks += 1
            print("✅ Found transition plan call in workflow")
        else:
            print("❌ Missing transition plan call in workflow")
        
        if passed_checks >= 5:
            print(f"✅ Level-4 transition plan implemented ({passed_checks}/6 checks)")
            return True
        else:
            print(f"❌ Level-4 transition plan incomplete ({passed_checks}/6 checks)")
            return False
            
    except Exception as e:
        print(f"❌ Level-4 transition plan test failed: {e}")
        return False

def main():
    """Run all fix tests"""
    print("🚀 Testing All Complete Fixes")
    print("=" * 60)
    
    tests = [
        ("Quick Org Discovery Fix", test_quick_org_discovery_fix),
        ("Organization Fixed Values", test_organization_fixed_values),
        ("Workflow Progression", test_workflow_progression),
        ("Enhanced Search Queries", test_enhanced_search_queries),
        ("Level-4 Transition Plan", test_level_4_transition_plan)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 COMPLETE FIXES TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}")
        if success:
            passed += 1
    
    print(f"\n🏁 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 ALL FIXES IMPLEMENTED SUCCESSFULLY!")
        print("\n💡 Fixed issues:")
        print("1. ✅ AttributeError: 'NoneType' object has no attribute 'query'")
        print("2. ✅ off_peak_hours and peak_hours fixed values (0.466, 0.9)")
        print("3. ✅ Workflow progression from org to plant level")
        print("4. ✅ Enhanced closure_year and mandatory_closure searches")
        print("5. ✅ Level-4 transition plan JSON structure")
        
        print("\n🚀 Expected behavior:")
        print("- No more AttributeError crashes")
        print("- Organization JSON shows off_peak_hours=0.466, peak_hours=0.9")
        print("- Agent proceeds from org level to plant level automatically")
        print("- Plant level includes closure information")
        print("- 4 levels: Organization, Plant, Unit, Transition Plan")
        
        print("\n🎯 Ready for production testing!")
    else:
        print(f"\n⚠️ {total - passed} fixes need attention")

if __name__ == "__main__":
    main()
