#!/usr/bin/env python3
"""
Test script to verify the PLF calculation fix
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), "backend", "src"))

from agent.fallback_calculations import FallbackCalculationEngine

def test_plf_unit_conversion():
    """Test PLF calculation with different unit formats"""
    
    print("🧪 TESTING PLF UNIT CONVERSION FIX")
    print("=" * 50)
    
    engine = FallbackCalculationEngine()
    
    # Test cases with different unit scenarios
    test_cases = [
        {
            "name": "Correct MWh Units",
            "capacity": "660",  # 660 MW
            "generation": [{"value": "3500", "year": "2023"}],  # 3500 MWh
            "expected_plf_range": (60, 70)  # Should be around 60.6%
        },
        {
            "name": "GWh Units (Common Issue)",
            "capacity": "660",  # 660 MW  
            "generation": [{"value": "3.5", "year": "2023"}],  # 3.5 GWh = 3500 MWh
            "expected_plf_range": (60, 70)  # Should be around 60.6%
        },
        {
            "name": "Large Numbers (Likely GWh)",
            "capacity": "660",  # 660 MW
            "generation": [{"value": "12400", "year": "2023"}],  # 12400 (likely GWh) = 12,400,000 MWh
            "expected_plf_range": (60, 70)  # Should be corrected to reasonable PLF
        },
        {
            "name": "Explicit GWh Units",
            "capacity": "660",  # 660 MW
            "generation": [{"value": "3.5 GWh", "year": "2023"}],  # 3.5 GWh = 3500 MWh
            "expected_plf_range": (60, 70)  # Should be around 60.6%
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 Test {i}: {test_case['name']}")
        print(f"   Capacity: {test_case['capacity']} MW")
        print(f"   Generation: {test_case['generation'][0]['value']}")
        
        # Create test data
        extracted_data = {
            "gross_power_generation": test_case["generation"]
        }
        
        unit_context = {
            "capacity": test_case["capacity"],
            "technology": "supercritical",
            "country": "United States"
        }
        
        # Test the PLF calculation
        plf_result = engine._calculate_plf_fallback(extracted_data, unit_context)
        
        if plf_result and len(plf_result) > 0:
            plf_value_str = plf_result[0]["value"]
            plf_value = float(plf_value_str.replace("%", ""))
            
            print(f"   Result PLF: {plf_value}%")
            
            # Check if PLF is in expected range
            min_plf, max_plf = test_case["expected_plf_range"]
            if min_plf <= plf_value <= max_plf:
                print(f"   ✅ PASSED: PLF is reasonable ({min_plf}-{max_plf}%)")
            elif plf_value > 100:
                print(f"   ❌ FAILED: PLF > 100% (unit conversion issue)")
            else:
                print(f"   ⚠️  WARNING: PLF outside expected range but reasonable")
        else:
            print(f"   ❌ FAILED: No PLF calculated")
    
    print("\n" + "=" * 50)
    print("🔧 PLF CALCULATION FORMULA:")
    print("   PLF = (Annual Generation MWh) / (Capacity MW × 8760 hours) × 100")
    print("   Example: 3500 MWh / (660 MW × 8760 h) × 100 = 60.6%")
    
    print("\n🎯 UNIT CONVERSION LOGIC:")
    print("   • If PLF > 100% → Try dividing generation by 1000 (GWh→MWh)")
    print("   • If still > 100% → Try dividing by 1,000,000 (TWh→MWh)")
    print("   • Only accept PLF values between 0-100%")

def test_specific_problematic_case():
    """Test the specific case that was showing 214.48% PLF"""
    
    print("\n\n🏭 TESTING SPECIFIC PROBLEMATIC CASE")
    print("=" * 50)
    
    engine = FallbackCalculationEngine()
    
    # Simulate the problematic case
    extracted_data = {
        "gross_power_generation": [
            {"value": "12400", "year": "2024"},  # This was likely causing 214.48%
            {"value": "9000", "year": "2023"},   # This was likely causing 156.01%
        ]
    }
    
    unit_context = {
        "capacity": "660",  # Assuming 660 MW unit
        "technology": "supercritical",
        "country": "United States"
    }
    
    print("Input data:")
    print(f"   Capacity: {unit_context['capacity']} MW")
    print(f"   Generation data: {extracted_data['gross_power_generation']}")
    
    # Test the PLF calculation
    plf_result = engine._calculate_plf_fallback(extracted_data, unit_context)
    
    print("\nResults:")
    if plf_result:
        for result in plf_result:
            plf_value = float(result["value"].replace("%", ""))
            print(f"   Year {result['year']}: {result['value']}")
            
            if plf_value <= 100:
                print(f"      ✅ FIXED: PLF is now reasonable")
            else:
                print(f"      ❌ STILL BROKEN: PLF > 100%")
    else:
        print("   ❌ No PLF data generated")

if __name__ == "__main__":
    test_plf_unit_conversion()
    test_specific_problematic_case()