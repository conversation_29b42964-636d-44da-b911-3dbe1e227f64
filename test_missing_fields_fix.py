#!/usr/bin/env python3
# Test results: ✅ ALL 4 CRITICAL FIELDS SUCCESSFULLY IMPLEMENTED!
"""
Test script to verify the 4 missing fields fix
Tests PLF, PAF, gross_power_generation, and emission_factor calculations
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), "backend", "src"))

from agent.fallback_calculations import FallbackCalculationEngine

def test_missing_fields_fix():
    """Test the implementation of missing fields fix"""
    
    print("🧪 TESTING MISSING FIELDS IMPLEMENTATION")
    print("=" * 60)
    
    # Initialize the calculation engine
    engine = FallbackCalculationEngine()
    
    # Test data - simulating empty search extraction (worst case)
    empty_extracted_data = {
        "capacity": "500",
        "technology": "supercritical",
        "fuel_type": [{"fuel": "Coal", "type": "bituminous", "years_percentage": {}}]
    }
    
    unit_context = {
        "capacity": 500,
        "technology": "supercritical", 
        "country": "India",
        "plant_name": "Test Power Plant",
        "unit_number": "1"
    }
    
    print(f"📊 TEST SCENARIO:")
    print(f"   Capacity: {unit_context['capacity']}MW")
    print(f"   Technology: {unit_context['technology']}")
    print(f"   Country: {unit_context['country']}")
    print(f"   Extracted Data: {len(empty_extracted_data)} basic fields only")
    print()
    
    # Test the enhancement
    print("🔧 RUNNING ENHANCEMENT...")
    enhanced_data = engine.enhance_unit_data(empty_extracted_data, unit_context, "test_session")
    
    print("\n✅ RESULTS ANALYSIS:")
    print("=" * 60)
    
    # Test 1: Gross Power Generation
    generation_data = enhanced_data.get("gross_power_generation", [])
    print(f"1. GROSS POWER GENERATION:")
    if generation_data:
        print(f"   ✅ Generated: {len(generation_data)} years of data")
        if generation_data:
            sample = generation_data[0]
            print(f"   📊 Sample: {sample['value']} MWh for {sample['year']}")
            print(f"   🔧 Method: {sample.get('_method', 'Unknown')}")
    else:
        print(f"   ❌ FAILED: No generation data generated")
    
    print()
    
    # Test 2: PLF
    plf_data = enhanced_data.get("plf", [])
    print(f"2. PLANT LOAD FACTOR (PLF):")
    if plf_data:
        print(f"   ✅ Generated: {len(plf_data)} years of data")
        if plf_data:
            sample = plf_data[0]
            print(f"   📊 Sample: {sample['value']} for {sample['year']}")
            print(f"   🔧 Method: {sample.get('_method', 'Unknown')}")
    else:
        print(f"   ❌ FAILED: No PLF data generated")
    
    print()
    
    # Test 3: PAF  
    paf_data = enhanced_data.get("PAF", [])
    print(f"3. PLANT AVAILABILITY FACTOR (PAF):")
    if paf_data:
        print(f"   ✅ Generated: {len(paf_data)} years of data")
        if paf_data:
            sample = paf_data[0]
            print(f"   📊 Sample: {sample['value']} for {sample['year']}")
            print(f"   🔧 Method: {sample.get('_method', 'Unknown')}")
    else:
        print(f"   ❌ FAILED: No PAF data generated")
    
    print()
    
    # Test 4: Emission Factor
    emission_data = enhanced_data.get("emission_factor", [])
    print(f"4. EMISSION FACTOR:")
    if emission_data:
        print(f"   ✅ Generated: {len(emission_data)} years of data")
        if emission_data:
            sample = emission_data[0]
            print(f"   📊 Sample: {sample['value']} kg CO2e/kWh for {sample['year']}")
            print(f"   🔧 Method: {sample.get('_method', 'Unknown')}")
    else:
        print(f"   ❌ FAILED: No emission factor data generated")
    
    print()
    print("🎯 SUMMARY:")
    print("=" * 60)
    
    success_count = 0
    total_tests = 4
    
    if generation_data: success_count += 1
    if plf_data: success_count += 1  
    if paf_data: success_count += 1
    if emission_data: success_count += 1
    
    print(f"✅ SUCCESS: {success_count}/{total_tests} critical fields implemented")
    
    if success_count == total_tests:
        print("🎉 ALL CRITICAL FIELDS SUCCESSFULLY IMPLEMENTED!")
        print("💡 The system should now generate these fields even when search extraction fails")
    else:
        print("⚠️  Some fields still need attention")
    
    return success_count == total_tests

if __name__ == "__main__":
    success = test_missing_fields_fix()
    sys.exit(0 if success else 1)