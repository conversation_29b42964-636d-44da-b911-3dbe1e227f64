# Gemini Fullstack LangGraph Project Information

## Summary
A fullstack application demonstrating a research-augmented conversational AI using LangGraph and Google's Gemini models. The system features a React frontend and a LangGraph-powered backend agent that performs comprehensive research by generating search terms, querying the web, reflecting on results, and providing well-supported answers with citations.

## Repository Structure
- **frontend/**: React application built with Vite, TypeScript, and Tailwind CSS
- **backend/**: LangGraph/FastAPI application with the research agent logic
- **Documentations/**: Project documentation files
- **.zencoder/**: Configuration and documentation directory
- **.vscode/**: VS Code configuration files

## Projects

### Backend (LangGraph Agent)
**Configuration File**: backend/pyproject.toml

#### Language & Runtime
**Language**: Python
**Version**: >=3.9,<4.0
**Build System**: setuptools
**Package Manager**: pip

#### Dependencies
**Main Dependencies**:
- langgraph (>=0.2.6)
- langchain (>=0.3.19)
- langchain-google-genai
- fastapi
- google-genai
- boto3
- sqlalchemy (>=2.0.0)
- psycopg2-binary

**Development Dependencies**:
- mypy (>=1.11.1)
- ruff (>=0.6.1)
- pytest (>=8.3.5)

#### Build & Installation
```bash
cd backend
pip install .
```

#### Docker
**Dockerfile**: Dockerfile
**Base Image**: docker.io/langchain/langgraph-api:3.11
**Dependencies**: Redis, PostgreSQL
**Run Command**:
```bash
GEMINI_API_KEY=<key> LANGSMITH_API_KEY=<key> docker-compose up
```

#### Testing
**Framework**: pytest
**Test Location**: backend/*.py
**Run Command**:
```bash
cd backend
pytest
```

### Frontend (React Application)
**Configuration File**: frontend/package.json

#### Language & Runtime
**Language**: TypeScript
**Version**: TypeScript ~5.7.2
**Build System**: Vite
**Package Manager**: npm

#### Dependencies
**Main Dependencies**:
- react (^19.0.0)
- react-dom (^19.0.0)
- @langchain/core (^0.3.55)
- @langchain/langgraph-sdk (^0.0.74)
- react-markdown (^9.0.3)
- react-router-dom (^7.5.3)
- tailwindcss (^4.1.5)

**Development Dependencies**:
- @vitejs/plugin-react-swc (^3.9.0)
- typescript (^5.7.2)
- eslint (^9.22.0)
- vite (^6.3.4)

#### Build & Installation
```bash
cd frontend
npm install
npm run build
```

#### Usage & Operations
**Development**:
```bash
cd frontend
npm run dev
```
**Production Build**:
```bash
cd frontend
npm run build
```

## Deployment
The application can be deployed using Docker and docker-compose. The Dockerfile builds the frontend and sets up the backend in a single container. The docker-compose.yml file configures the required services:

1. **langgraph-redis**: Redis instance for pub-sub broker
2. **langgraph-postgres**: PostgreSQL database for storing assistants, threads, and runs
3. **langgraph-api**: The main application container

The application is accessible at:
- Frontend: http://localhost:8123/app/
- API: http://localhost:8123

Environment variables required for deployment:
- GEMINI_API_KEY
- LANGSMITH_API_KEY