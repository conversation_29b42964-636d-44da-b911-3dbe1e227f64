#!/usr/bin/env python3
"""
Simple test script to verify data type normalization functionality
"""

import json
from typing import Dict, List, Optional, Any

class SimpleNormalizer:
    """Simplified version of the normalization methods"""
    
    def normalize_data_types_and_ranges(self, enhanced_data: Dict, unit_context: Dict) -> List[str]:
        """Normalize data types and value ranges to match reference.json format"""
        normalizations_performed = []
        
        try:
            print("🔧 NORMALIZING DATA TYPES: Starting data type and range normalization")
            
            # 1. Normalize PLF values (percentage strings to decimal fractions)
            if "plf" in enhanced_data and enhanced_data["plf"]:
                plf_normalized = self._normalize_percentage_time_series(enhanced_data["plf"], "PLF")
                if plf_normalized:
                    enhanced_data["plf"] = plf_normalized
                    normalizations_performed.append("PLF Data Types")
            
            # 2. Normalize Auxiliary Power Consumed (percentage strings to decimal fractions)
            if "auxiliary_power_consumed" in enhanced_data and enhanced_data["auxiliary_power_consumed"]:
                aux_normalized = self._normalize_percentage_time_series(enhanced_data["auxiliary_power_consumed"], "Auxiliary Power")
                if aux_normalized:
                    enhanced_data["auxiliary_power_consumed"] = aux_normalized
                    normalizations_performed.append("Auxiliary Power Data Types")
            
            # 3. Normalize PAF values (string to numeric)
            if "PAF" in enhanced_data and enhanced_data["PAF"]:
                paf_normalized = self._normalize_numeric_time_series(enhanced_data["PAF"], "PAF")
                if paf_normalized:
                    enhanced_data["PAF"] = paf_normalized
                    normalizations_performed.append("PAF Data Types")
            
            # 4. Normalize Gross Power Generation (string to numeric, handle unit conversion)
            if "gross_power_generation" in enhanced_data and enhanced_data["gross_power_generation"]:
                gen_normalized = self._normalize_generation_data(enhanced_data["gross_power_generation"], unit_context)
                if gen_normalized:
                    enhanced_data["gross_power_generation"] = gen_normalized
                    normalizations_performed.append("Generation Data Types")
            
            # 5. Normalize Emission Factor (string to numeric)
            if "emission_factor" in enhanced_data and enhanced_data["emission_factor"]:
                emission_normalized = self._normalize_numeric_time_series(enhanced_data["emission_factor"], "Emission Factor")
                if emission_normalized:
                    enhanced_data["emission_factor"] = emission_normalized
                    normalizations_performed.append("Emission Factor Data Types")
            
            # 6. Normalize Coal Unit Efficiency (percentage string to decimal)
            if "coal_unit_efficiency" in enhanced_data and enhanced_data["coal_unit_efficiency"]:
                efficiency_normalized = self._normalize_percentage_value(enhanced_data["coal_unit_efficiency"], "Coal Unit Efficiency")
                if efficiency_normalized is not None:
                    enhanced_data["coal_unit_efficiency"] = efficiency_normalized
                    normalizations_performed.append("Efficiency Data Types")
            
            # 7. Normalize GCV values (string to numeric)
            gcv_fields = ["gcv_coal", "gcv_biomass", "gcv_natural_gas"]
            for field in gcv_fields:
                if field in enhanced_data and enhanced_data[field]:
                    gcv_normalized = self._normalize_numeric_value(enhanced_data[field], field)
                    if gcv_normalized is not None:
                        enhanced_data[field] = gcv_normalized
                        normalizations_performed.append(f"{field} Data Types")
            
            # 8. Normalize Fuel Years Percentage (align with reference format)
            if "fuel_type" in enhanced_data and enhanced_data["fuel_type"]:
                fuel_normalized = self._normalize_fuel_years_percentage(enhanced_data["fuel_type"])
                if fuel_normalized:
                    enhanced_data["fuel_type"] = fuel_normalized
                    normalizations_performed.append("Fuel Years Percentage Format")
            
            if normalizations_performed:
                print(f"✅ DATA TYPE NORMALIZATION COMPLETED: {', '.join(normalizations_performed)}")
            else:
                print("ℹ️  No data type normalization needed")
                
        except Exception as e:
            print(f"❌ DATA TYPE NORMALIZATION ERROR: {str(e)}")
        
        return normalizations_performed
    
    def _extract_numeric_value(self, value: Any) -> float:
        """Extract numeric value from various formats"""
        if isinstance(value, (int, float)):
            return float(value)
        
        if isinstance(value, str):
            # Remove common non-numeric characters
            cleaned = value.replace(",", "").replace("MW", "").replace("GW", "").replace("%", "").strip()
            try:
                return float(cleaned)
            except:
                return 0.0
        
        return 0.0
    
    def _normalize_percentage_time_series(self, data: List[Dict], field_name: str) -> Optional[List[Dict]]:
        """Convert percentage string time series to decimal fractions"""
        try:
            normalized_data = []
            changes_made = False
            
            for item in data:
                if isinstance(item, dict) and "value" in item:
                    original_value = item["value"]
                    
                    # Convert percentage string to decimal
                    if isinstance(original_value, str) and "%" in original_value:
                        # Remove % and convert to decimal
                        numeric_str = original_value.replace("%", "").strip()
                        try:
                            percentage_value = float(numeric_str)
                            decimal_value = percentage_value / 100.0  # Convert to decimal fraction
                            
                            normalized_item = item.copy()
                            normalized_item["value"] = decimal_value
                            normalized_data.append(normalized_item)
                            changes_made = True
                            
                            print(f"🔧 {field_name}: {original_value} → {decimal_value}")
                        except ValueError:
                            normalized_data.append(item)  # Keep original if conversion fails
                    else:
                        normalized_data.append(item)  # Keep non-percentage values as-is
                else:
                    normalized_data.append(item)
            
            return normalized_data if changes_made else None
            
        except Exception as e:
            print(f"Error normalizing {field_name} percentage time series: {str(e)}")
            return None
    
    def _normalize_numeric_time_series(self, data: List[Dict], field_name: str) -> Optional[List[Dict]]:
        """Convert string time series to numeric values"""
        try:
            normalized_data = []
            changes_made = False
            
            for item in data:
                if isinstance(item, dict) and "value" in item:
                    original_value = item["value"]
                    
                    # Convert string to numeric
                    if isinstance(original_value, str):
                        try:
                            numeric_value = float(original_value)
                            
                            normalized_item = item.copy()
                            normalized_item["value"] = numeric_value
                            normalized_data.append(normalized_item)
                            changes_made = True
                            
                            print(f"🔧 {field_name}: '{original_value}' → {numeric_value}")
                        except ValueError:
                            normalized_data.append(item)  # Keep original if conversion fails
                    else:
                        normalized_data.append(item)  # Keep non-string values as-is
                else:
                    normalized_data.append(item)
            
            return normalized_data if changes_made else None
            
        except Exception as e:
            print(f"Error normalizing {field_name} numeric time series: {str(e)}")
            return None
    
    def _normalize_generation_data(self, data: List[Dict], unit_context: Dict) -> Optional[List[Dict]]:
        """Convert generation data with unit conversion (kWh to MWh if needed)"""
        try:
            normalized_data = []
            changes_made = False
            capacity = self._extract_numeric_value(unit_context.get("capacity", 0))
            
            for item in data:
                if isinstance(item, dict) and "value" in item:
                    original_value = item["value"]
                    
                    if isinstance(original_value, str):
                        try:
                            numeric_value = float(original_value)
                            
                            # Check if value seems to be in kWh (too large for MWh)
                            # Compare with reference data ranges - if value > 10,000 MWh, likely in kWh
                            # Reference generation values are typically 2,000-6,000 MWh range
                            if numeric_value > 10000:
                                # Convert from kWh to MWh
                                converted_value = numeric_value / 1000
                                print(f"🔧 Generation: {original_value} kWh → {converted_value:.2f} MWh")
                                final_value = converted_value
                            else:
                                final_value = numeric_value
                            
                            normalized_item = item.copy()
                            normalized_item["value"] = final_value
                            normalized_data.append(normalized_item)
                            changes_made = True
                            
                        except ValueError:
                            normalized_data.append(item)  # Keep original if conversion fails
                    else:
                        normalized_data.append(item)  # Keep non-string values as-is
                else:
                    normalized_data.append(item)
            
            return normalized_data if changes_made else None
            
        except Exception as e:
            print(f"Error normalizing generation data: {str(e)}")
            return None
    
    def _normalize_percentage_value(self, value: Any, field_name: str) -> Optional[float]:
        """Convert percentage string to decimal fraction"""
        try:
            if isinstance(value, str) and "%" in value:
                # Remove % and convert to decimal
                numeric_str = value.replace("%", "").strip()
                try:
                    percentage_value = float(numeric_str)
                    decimal_value = percentage_value / 100.0  # Convert to decimal fraction
                    
                    print(f"🔧 {field_name}: {value} → {decimal_value}")
                    return decimal_value
                except ValueError:
                    return None
            
            return None  # No change needed
            
        except Exception as e:
            print(f"Error normalizing {field_name} percentage value: {str(e)}")
            return None
    
    def _normalize_numeric_value(self, value: Any, field_name: str) -> Optional[float]:
        """Convert string to numeric value"""
        try:
            if isinstance(value, str):
                try:
                    numeric_value = float(value)
                    print(f"🔧 {field_name}: '{value}' → {numeric_value}")
                    return numeric_value
                except ValueError:
                    return None
            
            return None  # No change needed
            
        except Exception as e:
            print(f"Error normalizing {field_name} numeric value: {str(e)}")
            return None
    
    def _normalize_fuel_years_percentage(self, fuel_data: List[Dict]) -> Optional[List[Dict]]:
        """Normalize fuel years percentage to match reference format"""
        try:
            normalized_data = []
            changes_made = False
            
            for fuel_item in fuel_data:
                if isinstance(fuel_item, dict) and "years_percentage" in fuel_item:
                    years_percentage = fuel_item["years_percentage"]
                    
                    if isinstance(years_percentage, dict):
                        normalized_years = {}
                        
                        for year, percentage in years_percentage.items():
                            if isinstance(percentage, str):
                                try:
                                    # Convert string percentage to decimal fraction
                                    numeric_percentage = float(percentage)
                                    decimal_fraction = numeric_percentage / 100.0
                                    normalized_years[year] = decimal_fraction
                                    changes_made = True
                                except ValueError:
                                    normalized_years[year] = percentage  # Keep original if conversion fails
                            else:
                                normalized_years[year] = percentage
                        
                        normalized_fuel = fuel_item.copy()
                        normalized_fuel["years_percentage"] = normalized_years
                        normalized_data.append(normalized_fuel)
                    else:
                        normalized_data.append(fuel_item)
                else:
                    normalized_data.append(fuel_item)
            
            if changes_made:
                print("🔧 Fuel Years Percentage: Converted string percentages to decimal fractions")
                return normalized_data
            
            return None  # No changes needed
            
        except Exception as e:
            print(f"Error normalizing fuel years percentage: {str(e)}")
            return None


def test_data_normalization():
    """Test the data normalization with unit_output.json"""
    
    # Load the unit_output.json file
    with open('unit_output.json', 'r') as f:
        unit_data = json.load(f)
    
    # Create unit context from the data
    unit_context = {
        "capacity": unit_data.get("capacity", "590"),
        "technology": unit_data.get("technology", "supercritical"),
        "country": "default"
    }
    
    print("🔧 TESTING DATA TYPE NORMALIZATION")
    print("=" * 50)
    
    # Show original data types
    print("\n📊 ORIGINAL DATA TYPES:")
    print(f"PLF values: {type(unit_data['plf'][0]['value'])} - {unit_data['plf'][0]['value']}")
    print(f"Auxiliary Power: {type(unit_data['auxiliary_power_consumed'][1]['value'])} - {unit_data['auxiliary_power_consumed'][1]['value']}")
    print(f"PAF values: {type(unit_data['PAF'][0]['value'])} - {unit_data['PAF'][0]['value']}")
    print(f"Generation: {type(unit_data['gross_power_generation'][0]['value'])} - {unit_data['gross_power_generation'][0]['value']}")
    print(f"Emission Factor: {type(unit_data['emission_factor'][0]['value'])} - {unit_data['emission_factor'][0]['value']}")
    print(f"Coal Efficiency: {type(unit_data['coal_unit_efficiency'])} - {unit_data['coal_unit_efficiency']}")
    print(f"GCV Coal: {type(unit_data['gcv_coal'])} - {unit_data['gcv_coal']}")
    
    # Initialize the normalizer
    normalizer = SimpleNormalizer()
    
    # Make a copy for normalization
    normalized_data = json.loads(json.dumps(unit_data))
    
    # Apply normalization
    normalizations = normalizer.normalize_data_types_and_ranges(normalized_data, unit_context)
    
    print("\n📊 NORMALIZED DATA TYPES:")
    print(f"PLF values: {type(normalized_data['plf'][0]['value'])} - {normalized_data['plf'][0]['value']}")
    print(f"Auxiliary Power: {type(normalized_data['auxiliary_power_consumed'][1]['value'])} - {normalized_data['auxiliary_power_consumed'][1]['value']}")
    print(f"PAF values: {type(normalized_data['PAF'][0]['value'])} - {normalized_data['PAF'][0]['value']}")
    print(f"Generation: {type(normalized_data['gross_power_generation'][0]['value'])} - {normalized_data['gross_power_generation'][0]['value']}")
    print(f"Emission Factor: {type(normalized_data['emission_factor'][0]['value'])} - {normalized_data['emission_factor'][0]['value']}")
    print(f"Coal Efficiency: {type(normalized_data['coal_unit_efficiency'])} - {normalized_data['coal_unit_efficiency']}")
    print(f"GCV Coal: {type(normalized_data['gcv_coal'])} - {normalized_data['gcv_coal']}")
    
    # Show fuel years percentage normalization
    print(f"\nFuel Years Percentage (Coal 2024): {type(normalized_data['fuel_type'][0]['years_percentage']['2024'])} - {normalized_data['fuel_type'][0]['years_percentage']['2024']}")
    
    # Save normalized data for comparison
    with open('unit_output_normalized.json', 'w') as f:
        json.dump(normalized_data, f, indent=2)
    
    print("\n✅ NORMALIZATION TEST COMPLETED")
    print("📁 Normalized data saved to: unit_output_normalized.json")
    print(f"🔧 Total normalizations performed: {len(normalizations)}")

if __name__ == "__main__":
    test_data_normalization()