#!/usr/bin/env python3
"""
Comprehensive check for all new fields added yesterday across all three levels
"""

import json
import re

def check_organization_level_fields():
    """Check organization level new fields"""
    print("🔍 CHECKING ORGANIZATION LEVEL NEW FIELDS")
    print("=" * 60)
    
    # Expected new fields for organization level
    org_new_fields = {
        "off_peak_hours": {"expected_value": 0.466, "description": "Fixed off-peak hours value"},
        "peak_hours": {"expected_value": 0.9, "description": "Fixed peak hours value"}
    }
    
    try:
        # Check if fields are enforced in storage
        with open('backend/src/agent/json_s3_storage.py', 'r') as f:
            storage_content = f.read()
        
        # Check if fields are in prompts
        with open('backend/src/agent/graph.py', 'r') as f:
            graph_content = f.read()
        
        results = {}
        
        for field, info in org_new_fields.items():
            print(f"\n📋 Checking field: {field}")
            print(f"   Description: {info['description']}")
            print(f"   Expected value: {info['expected_value']}")
            
            # Check enforcement in storage
            enforcement_pattern = f'org_data\\["{field}"\\] = {info["expected_value"]}'
            if re.search(enforcement_pattern, storage_content):
                print(f"   ✅ Enforced in storage: {enforcement_pattern}")
                storage_enforced = True
            else:
                print(f"   ❌ NOT enforced in storage")
                storage_enforced = False
            
            # Check in prompts/examples
            if str(info["expected_value"]) in graph_content:
                print(f"   ✅ Found in prompts/examples")
                in_prompts = True
            else:
                print(f"   ❌ NOT found in prompts")
                in_prompts = False
            
            results[field] = {
                "storage_enforced": storage_enforced,
                "in_prompts": in_prompts,
                "overall": storage_enforced and in_prompts
            }
        
        return results
        
    except Exception as e:
        print(f"❌ Error checking organization fields: {e}")
        return {}

def check_plant_level_fields():
    """Check plant level new fields"""
    print("\n🔍 CHECKING PLANT LEVEL NEW FIELDS")
    print("=" * 60)
    
    # Expected new fields for plant level
    plant_new_fields = {
        "closure_year": {"description": "Scheduled closure year for the plant"},
        "mandatory_closure": {"description": "Mandatory closure requirements/policies"},
        "potential_reference": {"description": "Reference plant with lat/long coordinates"}
    }
    
    try:
        with open('backend/src/agent/graph.py', 'r') as f:
            content = f.read()
        
        results = {}
        
        for field, info in plant_new_fields.items():
            print(f"\n📋 Checking field: {field}")
            print(f"   Description: {info['description']}")
            
            # Check if field has search queries
            search_pattern = f'{field}_query'
            if search_pattern in content:
                print(f"   ✅ Has dedicated search query: {search_pattern}")
                has_search = True
            else:
                print(f"   ❌ NO dedicated search query")
                has_search = False
            
            # Check if field is assigned to plant_data
            assignment_pattern = f'plant_data\\["{field}"\\]'
            if re.search(assignment_pattern, content):
                print(f"   ✅ Assigned to plant_data: {assignment_pattern}")
                is_assigned = True
            else:
                print(f"   ❌ NOT assigned to plant_data")
                is_assigned = False
            
            # Check if field appears in plant template
            try:
                with open('plant_level-2.json', 'r') as f:
                    plant_template = json.load(f)
                if field in plant_template:
                    print(f"   ✅ Present in plant template")
                    in_template = True
                else:
                    print(f"   ❌ NOT in plant template")
                    in_template = False
            except:
                in_template = False
            
            results[field] = {
                "has_search": has_search,
                "is_assigned": is_assigned,
                "in_template": in_template,
                "overall": has_search and is_assigned and in_template
            }
        
        return results
        
    except Exception as e:
        print(f"❌ Error checking plant fields: {e}")
        return {}

def check_unit_level_fields():
    """Check unit level new fields"""
    print("\n🔍 CHECKING UNIT LEVEL NEW FIELDS")
    print("=" * 60)
    
    # Expected new fields for unit level (from unit_level.json)
    unit_new_fields = {
        "annual_operational_hours": {"expected": 8760, "description": "Annual operational hours"},
        "blending_percentage": {"expected": 0.15, "description": "Biomass blending percentage"},
        "fgds_status": {"expected": "string", "description": "FGDS system status"},
        "emission_factor_coking_coal": {"expected": "number", "description": "Coal emission factor"},
        "emission_factor_gas": {"expected": 2.69, "description": "Gas emission factor"},
        "heat_rate": {"expected": "number", "description": "Heat rate in Kcal/kWh"},
        "capex_required_renovation": {"expected": "number", "description": "CAPEX for renovation"},
        "ramp_up_rate": {"expected": "number", "description": "Ramp up rate"},
        "ramp_down_rate": {"expected": "number", "description": "Ramp down rate"}
    }
    
    try:
        # Check unit template
        with open('unit_level.json', 'r') as f:
            unit_template = json.load(f)
        
        # Check unit prompts
        with open('backend/src/agent/graph.py', 'r') as f:
            content = f.read()
        
        results = {}
        
        for field, info in unit_new_fields.items():
            print(f"\n📋 Checking field: {field}")
            print(f"   Description: {info['description']}")
            print(f"   Expected: {info['expected']}")
            
            # Check if in template
            if field in unit_template:
                print(f"   ✅ Present in unit template")
                in_template = True
            else:
                print(f"   ❌ NOT in unit template")
                in_template = False
            
            # Check if mentioned in prompts
            if field.replace('_', ' ') in content.lower() or field in content:
                print(f"   ✅ Mentioned in prompts")
                in_prompts = True
            else:
                print(f"   ❌ NOT mentioned in prompts")
                in_prompts = False
            
            # Check specific prompt patterns
            specific_checks = {
                "annual_operational_hours": "8760 hours",
                "blending_percentage": "biomass co-firing",
                "fgds_status": "FGDS.*status",
                "emission_factor_gas": "2.69 kg CO₂e/kg",
                "heat_rate": "heat rate.*Kcal/kWh"
            }
            
            has_specific = False
            if field in specific_checks:
                pattern = specific_checks[field]
                if re.search(pattern, content, re.IGNORECASE):
                    print(f"   ✅ Specific prompt found: {pattern}")
                    has_specific = True
                else:
                    print(f"   ❌ Specific prompt missing: {pattern}")
            
            results[field] = {
                "in_template": in_template,
                "in_prompts": in_prompts,
                "has_specific": has_specific,
                "overall": in_template and (in_prompts or has_specific)
            }
        
        return results
        
    except Exception as e:
        print(f"❌ Error checking unit fields: {e}")
        return {}

def check_level_4_transition_plan():
    """Check Level-4 transition plan implementation"""
    print("\n🔍 CHECKING LEVEL-4 TRANSITION PLAN")
    print("=" * 60)
    
    expected_fields = {
        "pk": "Organization UID",
        "sk": "transition_plan",
        "selected_plan_id": "Selected transition plan ID",
        "transitionPlanStratName": "Transition plan strategy name"
    }
    
    try:
        with open('backend/src/agent/json_s3_storage.py', 'r') as f:
            content = f.read()
        
        # Check if store_transition_plan_data function exists
        if 'def store_transition_plan_data' in content:
            print("✅ store_transition_plan_data function exists")
            function_exists = True
        else:
            print("❌ store_transition_plan_data function missing")
            function_exists = False
        
        # Check if all expected fields are in the function
        fields_present = {}
        for field, description in expected_fields.items():
            if f'"{field}"' in content:
                print(f"   ✅ Field present: {field} ({description})")
                fields_present[field] = True
            else:
                print(f"   ❌ Field missing: {field} ({description})")
                fields_present[field] = False
        
        # Check if function is called in the workflow
        with open('backend/src/agent/graph.py', 'r') as f:
            graph_content = f.read()
        
        if 'store_transition_plan_data' in graph_content:
            print("✅ Function is called in workflow")
            is_called = True
        else:
            print("❌ Function NOT called in workflow")
            is_called = False
        
        return {
            "function_exists": function_exists,
            "fields_present": fields_present,
            "is_called": is_called,
            "overall": function_exists and all(fields_present.values()) and is_called
        }
        
    except Exception as e:
        print(f"❌ Error checking Level-4: {e}")
        return {}

def main():
    """Run comprehensive check"""
    print("🔧 COMPREHENSIVE NEW FIELDS CHECK")
    print("=" * 70)
    
    # Run all checks
    org_results = check_organization_level_fields()
    plant_results = check_plant_level_fields()
    unit_results = check_unit_level_fields()
    level4_results = check_level_4_transition_plan()
    
    # Summary
    print("\n" + "=" * 70)
    print("📊 COMPREHENSIVE RESULTS SUMMARY")
    print("=" * 70)
    
    print("\n🏢 ORGANIZATION LEVEL:")
    for field, result in org_results.items():
        status = "✅ COMPLETE" if result["overall"] else "❌ INCOMPLETE"
        print(f"   {status} - {field}")
    
    print("\n🏭 PLANT LEVEL:")
    for field, result in plant_results.items():
        status = "✅ COMPLETE" if result["overall"] else "❌ INCOMPLETE"
        print(f"   {status} - {field}")
    
    print("\n⚡ UNIT LEVEL:")
    for field, result in unit_results.items():
        status = "✅ COMPLETE" if result["overall"] else "❌ INCOMPLETE"
        print(f"   {status} - {field}")
    
    print("\n🔄 LEVEL-4 TRANSITION PLAN:")
    status = "✅ COMPLETE" if level4_results.get("overall", False) else "❌ INCOMPLETE"
    print(f"   {status} - Transition Plan Implementation")
    
    # Overall assessment
    total_org = len([r for r in org_results.values() if r["overall"]])
    total_plant = len([r for r in plant_results.values() if r["overall"]])
    total_unit = len([r for r in unit_results.values() if r["overall"]])
    total_level4 = 1 if level4_results.get("overall", False) else 0
    
    total_complete = total_org + total_plant + total_unit + total_level4
    total_expected = len(org_results) + len(plant_results) + len(unit_results) + 1
    
    print(f"\n🎯 OVERALL: {total_complete}/{total_expected} field groups complete")
    
    if total_complete == total_expected:
        print("\n🎉 ALL NEW FIELDS PROPERLY IMPLEMENTED!")
        print("✅ Organization: off_peak_hours, peak_hours enforced")
        print("✅ Plant: closure_year, mandatory_closure, potential_reference")
        print("✅ Unit: All new technical fields with prompts")
        print("✅ Level-4: Transition plan structure complete")
    else:
        print(f"\n⚠️ {total_expected - total_complete} field groups need attention")
        print("Some new fields may not be properly prompted or implemented")

if __name__ == "__main__":
    main()
