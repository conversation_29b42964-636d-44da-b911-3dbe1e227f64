#!/usr/bin/env python3
"""
Comprehensive diagnostic script to identify units_id extraction issues
"""

import re
import json

def analyze_plant_level_prompt():
    """Analyze the plant-level prompt for units_id instructions"""
    print("🔍 ANALYZING PLANT-LEVEL PROMPT")
    print("=" * 50)
    
    try:
        with open('backend/src/agent/graph.py', 'r') as f:
            content = f.read()
        
        # Find the plant-level prompt
        prompt_start = content.find('units_id: List of unit numbers (as integers)')
        if prompt_start != -1:
            # Extract the full instruction
            prompt_section = content[prompt_start:prompt_start+500]
            print("✅ Found units_id instruction in plant-level prompt:")
            print(f"   {prompt_section[:200]}...")
            
            # Check if it has good guidance
            if "Unit 1" in prompt_section and "generating units" in prompt_section:
                print("✅ Prompt has good unit detection guidance")
                return True
            else:
                print("❌ Prompt lacks specific unit detection guidance")
                return False
        else:
            print("❌ units_id instruction not found in plant-level prompt")
            return False
            
    except Exception as e:
        print(f"❌ Error analyzing prompt: {e}")
        return False

def analyze_fallback_logic():
    """Analyze the fallback logic for empty units_id"""
    print("\n🔍 ANALYZING FALLBACK LOGIC")
    print("=" * 50)
    
    try:
        with open('backend/src/agent/graph.py', 'r') as f:
            content = f.read()
        
        # Check for fallback logic
        fallback_patterns = [
            'units_id is empty, applying fallback logic',
            'estimated_units = max(1, min(8, int(capacity / 300)))',
            'units_id = [1, 2]',
            'Applied fallback units_id'
        ]
        
        all_found = True
        for pattern in fallback_patterns:
            if pattern in content:
                print(f"✅ Found fallback pattern: {pattern}")
            else:
                print(f"❌ Missing fallback pattern: {pattern}")
                all_found = False
        
        return all_found
        
    except Exception as e:
        print(f"❌ Error analyzing fallback logic: {e}")
        return False

def analyze_unit_detection_functions():
    """Analyze unit detection functions"""
    print("\n🔍 ANALYZING UNIT DETECTION FUNCTIONS")
    print("=" * 50)
    
    try:
        with open('backend/src/agent/graph.py', 'r') as f:
            content = f.read()
        
        # Check for key unit detection functions
        functions_to_check = [
            'def enhanced_unit_detection',
            'def extract_units_from_plant_data',
            'def filter_operational_units'
        ]
        
        all_found = True
        for func in functions_to_check:
            if func in content:
                print(f"✅ Found function: {func}")
            else:
                print(f"❌ Missing function: {func}")
                all_found = False
        
        # Check for GEM wiki patterns
        gem_patterns = [
            'gem.wiki',
            'global energy monitor',
            'Unit\\s+(\\d+).*?(\\d+)\\s*MW'
        ]
        
        for pattern in gem_patterns:
            if pattern in content:
                print(f"✅ Found GEM wiki pattern: {pattern}")
            else:
                print(f"❌ Missing GEM wiki pattern: {pattern}")
        
        return all_found
        
    except Exception as e:
        print(f"❌ Error analyzing unit detection functions: {e}")
        return False

def analyze_potential_issues():
    """Analyze potential issues that could cause empty units_id"""
    print("\n🔍 ANALYZING POTENTIAL ISSUES")
    print("=" * 50)
    
    issues_found = []
    
    try:
        with open('backend/src/agent/graph.py', 'r') as f:
            content = f.read()
        
        # Issue 1: Check if units_id is being overwritten
        if 'units_id = []' in content:
            issues_found.append("units_id being set to empty list somewhere")
        
        # Issue 2: Check if there's proper error handling
        if 'except Exception as e:' not in content[content.find('def enhanced_unit_detection'):content.find('def enhanced_unit_detection') + 2000]:
            issues_found.append("No error handling in unit detection")
        
        # Issue 3: Check if fallback is properly triggered
        fallback_trigger = 'if not units_id or len(units_id) == 0:'
        if fallback_trigger not in content:
            issues_found.append("Fallback logic not properly triggered")
        
        # Issue 4: Check if LLM response parsing is robust
        if 'json.loads' in content and 'try:' not in content[content.find('json.loads'):content.find('json.loads') + 100]:
            issues_found.append("JSON parsing without proper error handling")
        
        if issues_found:
            print("❌ POTENTIAL ISSUES FOUND:")
            for i, issue in enumerate(issues_found, 1):
                print(f"   {i}. {issue}")
        else:
            print("✅ No obvious issues found in code structure")
        
        return len(issues_found) == 0
        
    except Exception as e:
        print(f"❌ Error analyzing potential issues: {e}")
        return False

def suggest_fixes():
    """Suggest fixes for common units_id issues"""
    print("\n🔧 SUGGESTED FIXES")
    print("=" * 50)
    
    fixes = [
        {
            "issue": "LLM not extracting units_id from research data",
            "fix": "Enhance plant-level prompt with more specific examples and patterns",
            "priority": "HIGH"
        },
        {
            "issue": "Research phase not gathering enough unit information",
            "fix": "Add specific GEM wiki queries for unit specifications",
            "priority": "HIGH"
        },
        {
            "issue": "Fallback logic not triggering properly",
            "fix": "Add more robust empty check and logging",
            "priority": "MEDIUM"
        },
        {
            "issue": "Unit detection functions not finding units in text",
            "fix": "Improve regex patterns and add more unit name variations",
            "priority": "MEDIUM"
        },
        {
            "issue": "JSON parsing errors causing empty units_id",
            "fix": "Add better error handling and validation",
            "priority": "LOW"
        }
    ]
    
    for fix in fixes:
        priority_color = "🔴" if fix["priority"] == "HIGH" else "🟡" if fix["priority"] == "MEDIUM" else "🟢"
        print(f"{priority_color} {fix['priority']}: {fix['issue']}")
        print(f"   Fix: {fix['fix']}")
        print()

def create_test_cases():
    """Create test cases for units_id extraction"""
    print("\n🧪 TEST CASES FOR UNITS_ID EXTRACTION")
    print("=" * 50)
    
    test_cases = [
        {
            "name": "Normal Plant",
            "input": '{"units_id": [1, 2, 3, 4]}',
            "expected": [1, 2, 3, 4]
        },
        {
            "name": "Empty units_id",
            "input": '{"units_id": []}',
            "expected": "Should trigger fallback"
        },
        {
            "name": "Missing units_id field",
            "input": '{"plant_name": "Test Plant"}',
            "expected": "Should trigger fallback"
        },
        {
            "name": "String units",
            "input": '{"units_id": ["Unit 1", "Unit 2", "Unit 3"]}',
            "expected": [1, 2, 3]
        },
        {
            "name": "Duplicates (Taichung case)",
            "input": '{"units_id": [1,2,3,4,5,6,7,8,9,10,1,2,3,4]}',
            "expected": [1,2,3,4,5,6,7,8,9,10,11,12,13,14]
        }
    ]
    
    for case in test_cases:
        print(f"📋 {case['name']}:")
        print(f"   Input: {case['input']}")
        print(f"   Expected: {case['expected']}")
        print()

def main():
    """Run comprehensive diagnosis"""
    print("🔧 COMPREHENSIVE UNITS_ID DIAGNOSTIC")
    print("=" * 60)
    
    checks = [
        ("Plant-Level Prompt", analyze_plant_level_prompt),
        ("Fallback Logic", analyze_fallback_logic),
        ("Unit Detection Functions", analyze_unit_detection_functions),
        ("Potential Issues", analyze_potential_issues),
    ]
    
    results = []
    for check_name, check_func in checks:
        try:
            result = check_func()
            results.append((check_name, result))
        except Exception as e:
            print(f"❌ {check_name} failed with exception: {e}")
            results.append((check_name, False))
    
    print("\n" + "=" * 60)
    print("📊 DIAGNOSTIC RESULTS")
    print("=" * 60)
    
    passed = 0
    for check_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {check_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 OVERALL: {passed}/{len(results)} checks passed")
    
    # Always show suggestions
    suggest_fixes()
    create_test_cases()
    
    if passed < len(results):
        print("\n🚨 CRITICAL ISSUES DETECTED!")
        print("The units_id extraction system has problems that need immediate attention.")
        print("Focus on HIGH priority fixes first.")
    else:
        print("\n✅ SYSTEM STRUCTURE LOOKS GOOD")
        print("The issue might be in the LLM response or research data quality.")
        print("Consider testing with actual plant extraction to identify runtime issues.")

if __name__ == "__main__":
    main()
