#!/usr/bin/env python3
"""
Test script to verify the model and units_id fixes
"""

import requests
import json
import time

def test_model_fix():
    """Test that the model error is fixed"""
    print("🧪 Testing model configuration fix...")
    
    # Test data with a simple plant
    test_data = {
        "input": {
            "messages": [
                {
                    "role": "user",
                    "content": "Extract information about Mundra Thermal Power Station in India"
                }
            ]
        },
        "config": {
            "configurable": {
                "reasoning_model": "gemini-2.0-flash"  # Use the fixed model
            }
        },
        "assistant_id": "agent"  # Required parameter
    }
    
    try:
        # Send request to the backend
        response = requests.post(
            "http://127.0.0.1:2024/runs/stream",
            json=test_data,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        print(f"✅ Request sent successfully. Status: {response.status_code}")
        
        # Check if we get a response without the model error
        if response.status_code == 200:
            print("✅ No immediate model error - backend is responding")
            return True
        else:
            print(f"❌ Backend returned error: {response.status_code}")
            print(f"Response: {response.text[:200]}...")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
        return False

def test_configuration_values():
    """Test that configuration values are correct"""
    print("\n🧪 Testing configuration values...")
    
    try:
        # Read the configuration file directly
        with open('backend/src/agent/configuration.py', 'r') as f:
            config_content = f.read()
        
        # Check if the old model is removed
        if "gemini-2.5-flash-preview-04-17" in config_content:
            print("❌ Old model still found in configuration")
            return False
        
        # Check if the new model is present
        if "gemini-2.0-flash" in config_content:
            print("✅ New model found in configuration")
            return True
        else:
            print("❌ New model not found in configuration")
            return False
            
    except Exception as e:
        print(f"❌ Failed to read configuration: {e}")
        return False

def test_frontend_model():
    """Test that frontend model is updated"""
    print("\n🧪 Testing frontend model default...")
    
    try:
        # Read the frontend file
        with open('frontend/src/components/InputForm.tsx', 'r') as f:
            frontend_content = f.read()
        
        # Check if the old model is removed
        if "gemini-2.5-flash-preview-04-17" in frontend_content:
            print("❌ Old model still found in frontend")
            return False
        
        # Check if the new model is present as default
        if 'useState("gemini-2.0-flash")' in frontend_content:
            print("✅ New model found as default in frontend")
            return True
        else:
            print("❌ New model not found as default in frontend")
            return False
            
    except Exception as e:
        print(f"❌ Failed to read frontend file: {e}")
        return False

def test_units_id_prompt():
    """Test that units_id prompt is enhanced"""
    print("\n🧪 Testing units_id prompt enhancement...")
    
    try:
        # Read the graph file
        with open('backend/src/agent/graph.py', 'r') as f:
            graph_content = f.read()
        
        # Check if the enhanced prompt is present
        if "List of unit numbers (as integers)" in graph_content:
            print("✅ Enhanced units_id prompt found")
            return True
        else:
            print("❌ Enhanced units_id prompt not found")
            return False
            
    except Exception as e:
        print(f"❌ Failed to read graph file: {e}")
        return False

def test_units_id_fallback():
    """Test that units_id fallback logic is present"""
    print("\n🧪 Testing units_id fallback logic...")
    
    try:
        # Read the graph file
        with open('backend/src/agent/graph.py', 'r') as f:
            graph_content = f.read()
        
        # Check if the fallback logic is present
        if "units_id is empty, applying fallback logic" in graph_content:
            print("✅ units_id fallback logic found")
            return True
        else:
            print("❌ units_id fallback logic not found")
            return False
            
    except Exception as e:
        print(f"❌ Failed to read graph file: {e}")
        return False

def main():
    """Run all tests"""
    print("🔧 TESTING MODEL AND UNITS_ID FIXES")
    print("=" * 50)
    
    tests = [
        ("Configuration Values", test_configuration_values),
        ("Frontend Model", test_frontend_model),
        ("Units ID Prompt", test_units_id_prompt),
        ("Units ID Fallback", test_units_id_fallback),
        ("Model Fix", test_model_fix),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 50)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 OVERALL: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 ALL FIXES VERIFIED SUCCESSFULLY!")
        print("\n📋 SUMMARY OF FIXES:")
        print("1. ✅ Updated reflection_model from gemini-2.5-flash-preview-04-17 to gemini-2.0-flash")
        print("2. ✅ Updated frontend default model to gemini-2.0-flash")
        print("3. ✅ Enhanced units_id prompt with specific instructions")
        print("4. ✅ Added fallback logic for empty units_id")
        print("\n🚀 The system should now work without model errors and handle empty units_id properly!")
    else:
        print("⚠️ Some tests failed. Please check the output above.")

if __name__ == "__main__":
    main()
