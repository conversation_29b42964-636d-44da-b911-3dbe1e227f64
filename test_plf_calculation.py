#!/usr/bin/env python3
"""
Test script to verify the PLF calculation logic
"""

def calculate_plf(annual_generation_mwh: float, capacity_mw: float) -> float:
    """
    Calculate Plant Load Factor
    PLF = (Annual generation) / (Maximum generation)
    Maximum generation = Capacity (MW) × 8760 hours
    """
    max_generation_mwh = capacity_mw * 8760  # 8760 hours per year
    plf = (annual_generation_mwh / max_generation_mwh) * 100
    return round(plf, 2)

def extract_generation_value_with_unit_conversion(value_str: str) -> float:
    """Extract generation value and convert to MWh if needed"""
    if not isinstance(value_str, str):
        try:
            return float(value_str)
        except:
            return 0.0
    
    # Remove common characters and extract number
    cleaned = value_str.replace(",", "").replace("MW", "").replace("GW", "").replace("%", "").strip()
    
    # Check for unit indicators
    value_lower = value_str.lower()
    
    try:
        numeric_value = float(cleaned)
    except:
        return 0.0
    
    if numeric_value <= 0:
        return 0.0
    
    # Convert based on detected units
    if any(unit in value_lower for unit in ['gwh', 'gw-h', 'gw h']):
        # Convert GWh to MWh
        return numeric_value * 1000
    elif any(unit in value_lower for unit in ['twh', 'tw-h', 'tw h']):
        # Convert TWh to MWh
        return numeric_value * 1000000
    elif any(unit in value_lower for unit in ['kwh', 'kw-h', 'kw h']):
        # Convert kWh to MWh
        return numeric_value / 1000
    else:
        # Assume MWh if no unit specified
        return numeric_value

def calculate_plf_with_unit_detection(generation_str: str, capacity_mw: float) -> float:
    """Calculate PLF with intelligent unit detection and correction"""
    
    # Extract generation with unit conversion
    generation_mwh = extract_generation_value_with_unit_conversion(generation_str)
    
    if generation_mwh <= 0:
        return 0.0
    
    # Calculate PLF
    plf = calculate_plf(generation_mwh, capacity_mw)
    
    # Intelligent unit detection: if PLF > 100%, likely unit mismatch
    if plf > 100:
        # Try dividing by 1000 (GWh to MWh conversion)
        corrected_generation = generation_mwh / 1000
        corrected_plf = calculate_plf(corrected_generation, capacity_mw)
        
        if 20 <= corrected_plf <= 100:  # Reasonable PLF range
            print(f"[PLF Fix] Detected unit mismatch: {generation_mwh} → {corrected_generation} MWh")
            print(f"[PLF Fix] PLF corrected: {plf:.1f}% → {corrected_plf:.1f}%")
            return corrected_plf
        else:
            # Try dividing by 1000000 (TWh to MWh conversion)
            corrected_generation = generation_mwh / 1000000
            corrected_plf = calculate_plf(corrected_generation, capacity_mw)
            
            if 20 <= corrected_plf <= 100:  # Reasonable PLF range
                print(f"[PLF Fix] Detected TWh unit: {generation_mwh} → {corrected_generation} MWh")
                print(f"[PLF Fix] PLF corrected: {plf:.1f}% → {corrected_plf:.1f}%")
                return corrected_plf
    
    return plf

def test_plf_calculations():
    """Test PLF calculations with various scenarios"""
    
    print("🧪 TESTING PLF CALCULATION WITH UNIT DETECTION")
    print("=" * 60)
    
    # Test cases
    test_cases = [
        {
            "name": "Normal Case (Correct Units)",
            "capacity": 660,  # MW
            "generation": "3469000",  # MWh (for ~60% PLF)
            "expected_plf": 60.0
        },
        {
            "name": "Your Problematic Case 1 (GWh misinterpreted as MWh)",
            "capacity": 660,  # MW  
            "generation": "12400000",  # This gives 214% PLF - likely 12.4 GWh misread as MWh
            "expected_plf": 214.5  # This should get corrected to ~21.4%
        },
        {
            "name": "Your Problematic Case 2 (GWh misinterpreted as MWh)", 
            "capacity": 660,  # MW
            "generation": "9000000",  # This gives 156% PLF - likely 9 GWh misread as MWh
            "expected_plf": 156.0  # This should get corrected to ~15.6%
        },
        {
            "name": "Explicit GWh Units",
            "capacity": 660,  # MW
            "generation": "3469 GWh",  # 3469 GWh = 3,469,000 MWh
            "expected_plf": 60.0
        },
        {
            "name": "Small Number (Likely GWh)",
            "capacity": 660,  # MW
            "generation": "3469",  # Likely 3469 GWh = 3,469,000 MWh
            "expected_plf": 60.0
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 Test {i}: {test_case['name']}")
        print(f"   Capacity: {test_case['capacity']} MW")
        print(f"   Generation: {test_case['generation']}")
        print(f"   Expected PLF (before fix): ~{test_case['expected_plf']:.1f}%")
        
        # Calculate PLF with unit detection
        result_plf = calculate_plf_with_unit_detection(test_case['generation'], test_case['capacity'])
        
        print(f"   Result PLF: {result_plf:.1f}%")
        
        if 0 <= result_plf <= 100:
            print(f"   ✅ PASSED: PLF is reasonable")
        else:
            print(f"   ❌ FAILED: PLF is still unrealistic")
    
    print("\n" + "=" * 60)
    print("🔧 EXPLANATION:")
    print("The fix detects when PLF > 100% and tries unit conversions:")
    print("1. Divide by 1,000 (GWh → MWh)")
    print("2. Divide by 1,000,000 (TWh → MWh)")
    print("3. Only accept results in reasonable range (20-100%)")

def demonstrate_your_specific_issue():
    """Demonstrate the specific issue you reported"""
    
    print("\n\n🏭 YOUR SPECIFIC ISSUE DEMONSTRATION")
    print("=" * 60)
    
    # Your reported PLF values suggest these generation numbers
    capacity = 660  # Assuming 660 MW unit
    
    cases = [
        {"plf": 214.48, "year": "Annual"},
        {"plf": 156.01, "year": "2024"},
        {"plf": 179.69, "year": "2023"},
        {"plf": 188.95, "year": "2022"},
        {"plf": 194.51, "year": "2021"},
        {"plf": 181.54, "year": "2020"}
    ]
    
    print(f"Assuming capacity: {capacity} MW")
    print("Reverse-calculating the generation values that would cause your PLF:")
    
    for case in cases:
        # Reverse calculate what generation would cause this PLF
        max_generation = capacity * 8760  # MWh
        implied_generation = (case["plf"] / 100) * max_generation
        
        print(f"\n   {case['year']}: PLF {case['plf']}%")
        print(f"      Implied generation: {implied_generation:.0f} MWh")
        
        # Test the fix
        corrected_plf = calculate_plf_with_unit_detection(str(implied_generation), capacity)
        print(f"      After fix: {corrected_plf:.1f}%")

if __name__ == "__main__":
    test_plf_calculations()
    demonstrate_your_specific_issue()