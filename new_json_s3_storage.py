"""
JSON S3 Storage Module for Power Plant Research Data

This module handles uploading JSON data at different processing levels
(organization, plant, unit) to S3 with proper folder structure.
"""

import os
import json
import re
import boto3
from datetime import datetime
from typing import Dict, Any, Optional
from dotenv import load_dotenv

load_dotenv()

# S3 Configuration - Use S3-specific credentials
S3_AWS_ACCESS_KEY = os.getenv('S3_AWS_ACCESS_KEY_ID')
S3_AWS_SECRET_KEY = os.getenv('S3_AWS_SECRET_ACCESS_KEY')
AWS_REGION = os.getenv('AWS_REGION', 'ap-south-1')
S3_BUCKET = 'clem-transition-tech'

print(f"🔧 S3 Configuration:")
print(f"   - S3 Access Key: {S3_AWS_ACCESS_KEY[:8] + '...' if S3_AWS_ACCESS_KEY else 'NOT SET'}")
print(f"   - S3 Region: {AWS_REGION}")
print(f"   - S3 Bucket: {S3_BUCKET}")

def sanitize_plant_name(plant_name: str) -> str:
    """
    Convert power plant name to S3-safe folder name.
    
    Examples:
        "Jhajjar Power Plant" → "Jhajjar_Power_Plant"
        "NTPC Dadri (Stage-II)" → "NTPC_Dadri_Stage_II"
        "Adani Mundra Power Station" → "Adani_Mundra_Power_Station"
    """
    if not plant_name:
        return "Unknown_Plant"
    
    # Remove special characters except spaces and hyphens, replace & with _and_
    cleaned = plant_name.replace('&', '_and_')
    cleaned = re.sub(r'[^\w\s-]', '', cleaned)
    
    # Replace spaces and hyphens with underscores
    sanitized = cleaned.replace(' ', '_').replace('-', '_')
    
    # Remove multiple consecutive underscores
    sanitized = re.sub(r'_+', '_', sanitized)
    
    # Remove leading/trailing underscores
    sanitized = sanitized.strip('_')
    
    return sanitized if sanitized else "Unknown_Plant"

def upload_json_to_s3(
    json_data: Dict[Any, Any], 
    plant_folder: str, 
    filename: str,
    session_id: str = "unknown"
) -> Optional[str]:
    """
    Upload JSON data to S3 with the specified folder structure.
    
    Args:
        json_data: Dictionary containing the data to upload
        plant_folder: Sanitized plant name for folder structure
        filename: Name of the JSON file (e.g., 'organization_level.json')
        session_id: Session ID for logging
        
    Returns:
        S3 URL of uploaded file, or None if upload failed
    """
    try:
        # Initialize S3 client with S3-specific credentials
        s3_client = boto3.client(
            's3',
            aws_access_key_id=S3_AWS_ACCESS_KEY,
            aws_secret_access_key=S3_AWS_SECRET_KEY,
            region_name=AWS_REGION
        )
        
        # Add metadata to JSON
        enhanced_data = {
            **json_data,
            "metadata": {
                "uploaded_at": datetime.utcnow().isoformat() + "Z",
                "session_id": session_id,
                "plant_folder": plant_folder,
                "file_type": filename.replace('.json', ''),
                "bucket": S3_BUCKET
            }
        }
        
        # Convert to JSON string
        json_string = json.dumps(enhanced_data, indent=2, ensure_ascii=False)
        
        # Create S3 key (file path)
        s3_key = f"{plant_folder}/{filename}"
        
        # Upload to S3
        s3_client.put_object(
            Bucket=S3_BUCKET,
            Key=s3_key,
            Body=json_string,
            ContentType='application/json',
            ContentEncoding='utf-8'
        )
        
        # Generate S3 URL
        s3_url = f"https://{S3_BUCKET}.s3.amazonaws.com/{s3_key}"
        
        print(f"[Session {session_id}] ✅ JSON uploaded: {s3_url}")
        return s3_url
        
    except Exception as e:
        print(f"[Session {session_id}] ❌ Failed to upload {filename}: {str(e)}")
        return None

def store_organization_data(
    org_data: Dict[Any, Any], 
    plant_name: str, 
    session_id: str = "unknown",
    org_uid: str = None
) -> Optional[str]:
    """
    Store organization-level data to S3.
    
    Args:
        org_data: Organization data dictionary
        plant_name: Original plant name from user query
        session_id: Session ID for tracking
        org_uid: Organization UID (primary key)
        
    Returns:
        S3 URL of uploaded file
    """
    # Add UID to organization data as primary key
    print(f"[Session {session_id}] 🔍 DEBUG: org_uid = '{org_uid}'")
    print(f"[Session {session_id}] 🔍 DEBUG: org_data keys = {list(org_data.keys())}")
    print(f"[Session {session_id}] 🔍 DEBUG: current pk = '{org_data.get('pk', 'NOT_FOUND')}'")
    
    if org_uid:
        org_data["org_uid"] = org_uid
        # ALWAYS replace pk field with actual UID regardless of current value
        old_pk = org_data.get("pk", "NOT_FOUND")
        org_data["pk"] = org_uid
        print(f"[Session {session_id}] ✅ Set pk field: '{old_pk}' → '{org_uid}'")
        print(f"[Session {session_id}] 🔑 Added UID to organization data: {org_uid}")
    else:
        print(f"[Session {session_id}] ❌ No org_uid provided to store_organization_data")
        print(f"[Session {session_id}] 🔍 EMERGENCY FIX: Attempting to generate UID from org data...")

        # EMERGENCY FIX: Try to generate UID from organization data if not provided
        org_name = org_data.get("organization_name", "")
        country = org_data.get("country_name", "")

        if org_name and country:
            try:
                from agent.database_manager import get_database_manager
                db_manager = get_database_manager()
                emergency_uid = db_manager.generate_org_uid(org_name, country)

                org_data["org_uid"] = emergency_uid
                old_pk = org_data.get("pk", "NOT_FOUND")
                org_data["pk"] = emergency_uid
                print(f"[Session {session_id}] 🚨 EMERGENCY UID GENERATED: '{emergency_uid}'")
                print(f"[Session {session_id}] ✅ Set pk field: '{old_pk}' → '{emergency_uid}'")
            except Exception as e:
                print(f"[Session {session_id}] ❌ Emergency UID generation failed: {e}")
                # Last resort: set pk to null instead of "default null"
                org_data["pk"] = None
                print(f"[Session {session_id}] 🔧 Set pk to null as last resort")
        else:
            print(f"[Session {session_id}] ❌ Cannot generate emergency UID: missing org_name or country")
            # Last resort: set pk to null instead of "default null"
            org_data["pk"] = None
            print(f"[Session {session_id}] 🔧 Set pk to null as last resort")
    
    plant_folder = sanitize_plant_name(plant_name)
    filename = "organization_level.json"
    
    print(f"[Session {session_id}] 🏢 Storing organization data for: {plant_name}")
    print(f"[Session {session_id}] 📁 S3 folder: {plant_folder}")
    
    return upload_json_to_s3(org_data, plant_folder, filename, session_id)

def store_plant_data(
    plant_data: Dict[Any, Any], 
    plant_name: str, 
    session_id: str = "unknown",
    org_uid: str = None
) -> Optional[str]:
    """
    Store plant-level data to S3.
    
    Args:
        plant_data: Plant data dictionary
        plant_name: Original plant name from user query
        session_id: Session ID for tracking
        org_uid: Organization UID (primary key)
        
    Returns:
        S3 URL of uploaded file
    """
    # Add UID to plant data as primary key
    print(f"[Session {session_id}] 🔍 DEBUG: org_uid = '{org_uid}'")
    print(f"[Session {session_id}] 🔍 DEBUG: plant_data keys = {list(plant_data.keys())}")
    print(f"[Session {session_id}] 🔍 DEBUG: current pk = '{plant_data.get('pk', 'NOT_FOUND')}'")
    
    if org_uid:
        plant_data["org_uid"] = org_uid
        # ALWAYS replace pk field with actual UID regardless of current value
        old_pk = plant_data.get("pk", "NOT_FOUND")
        plant_data["pk"] = org_uid
        print(f"[Session {session_id}] ✅ Set pk field: '{old_pk}' → '{org_uid}'")
        print(f"[Session {session_id}] 🔑 Added UID to plant data: {org_uid}")
    else:
        print(f"[Session {session_id}] ❌ No org_uid provided to store_plant_data")
    
    plant_folder = sanitize_plant_name(plant_name)
    filename = "plant_level.json"
    
    print(f"[Session {session_id}] 🏭 Storing plant data for: {plant_name}")
    print(f"[Session {session_id}] 📁 S3 folder: {plant_folder}")
    
    return upload_json_to_s3(plant_data, plant_folder, filename, session_id)

def store_unit_data(
    unit_data: Dict[Any, Any], 
    plant_name: str, 
    unit_number: str,
    session_id: str = "unknown",
    org_uid: str = None
) -> Optional[str]:
    """
    Store individual unit data to S3.
    
    Args:
        unit_data: Unit data dictionary
        plant_name: Original plant name from user query
        unit_number: Unit number (e.g., "1", "2", "3")
        session_id: Session ID for tracking
        org_uid: Organization UID (primary key)
        
    Returns:
        S3 URL of uploaded file
    """
    # Add UID to unit data as primary key
    if org_uid:
        unit_data["org_uid"] = org_uid
        # ALWAYS replace pk field with actual UID regardless of current value
        old_pk = unit_data.get("pk", "NOT_FOUND")
        unit_data["pk"] = org_uid
        print(f"[Session {session_id}] ✅ Set pk field: '{old_pk}' → '{org_uid}'")
        print(f"[Session {session_id}] 🔑 Added UID to Unit {unit_number} data: {org_uid}")
    else:
        print(f"[Session {session_id}] ❌ No org_uid provided to store_unit_data")
    
    plant_folder = sanitize_plant_name(plant_name)
    filename = f"unit_{unit_number}.json"
    
    print(f"[Session {session_id}] ⚡ Storing Unit {unit_number} data for: {plant_name}")
    print(f"[Session {session_id}] 📁 S3 folder: {plant_folder}")
    
    return upload_json_to_s3(unit_data, plant_folder, filename, session_id)

def store_transition_plan_data(
    plant_name: str,
    session_id: str = "unknown",
    org_uid: str = None
) -> Optional[str]:
    """
    Store Level-4 transition plan data to S3.

    Args:
        plant_name: Original plant name from user query
        session_id: Session ID for tracking
        org_uid: Organization UID (primary key)

    Returns:
        S3 URL of uploaded file
    """
    # Create Level-4 transition plan JSON structure
    transition_plan_data = {
        "pk": org_uid if org_uid else "",
        "sk": "transition_plan",
        "selected_plan_id": "",
        "transitionPlanStratName": ""
    }

    print(f"[Session {session_id}] 🔍 DEBUG: Creating Level-4 transition plan")
    print(f"[Session {session_id}] 🔍 DEBUG: org_uid = '{org_uid}'")
    print(f"[Session {session_id}] 🔍 DEBUG: transition_plan_data = {transition_plan_data}")

    if org_uid:
        print(f"[Session {session_id}] 🔑 Added UID to transition plan: {org_uid}")
    else:
        print(f"[Session {session_id}] ❌ No org_uid provided to store_transition_plan_data")

    plant_folder = sanitize_plant_name(plant_name)
    filename = "transition_plan.json"

    print(f"[Session {session_id}] 📋 Storing Level-4 transition plan for: {plant_name}")
    print(f"[Session {session_id}] 📁 S3 folder: {plant_folder}")

    return upload_json_to_s3(transition_plan_data, plant_folder, filename, session_id)

def get_plant_s3_urls(plant_name: str, session_id: str = "unknown") -> Dict[str, Any]:
    """
    Generate S3 URLs for all expected files of a plant (for state tracking).
    
    Args:
        plant_name: Original plant name from user query
        session_id: Session ID for tracking
        
    Returns:
        Dictionary with URL structure for state management
    """
    plant_folder = sanitize_plant_name(plant_name)
    base_url = f"https://{S3_BUCKET}.s3.amazonaws.com/{plant_folder}"
    
    return {
        "plant_folder": plant_folder,
        "plant_name": plant_name,
        "base_url": base_url,
        "organization": f"{base_url}/organization_level.json",
        "plant": f"{base_url}/plant_level.json",
        "units": {}  # Will be populated as units are processed
    }

def check_s3_connection(session_id: str = "test") -> bool:
    """
    Test S3 connection and credentials.

    Returns:
        True if connection successful, False otherwise
    """
    try:
        s3_client = boto3.client(
            's3',
            aws_access_key_id=S3_AWS_ACCESS_KEY,
            aws_secret_access_key=S3_AWS_SECRET_KEY,
            region_name=AWS_REGION
        )

        # Try to list objects (this will fail if credentials are wrong)
        s3_client.head_bucket(Bucket=S3_BUCKET)
        print(f"[Session {session_id}] ✅ S3 connection successful to bucket: {S3_BUCKET}")
        return True

    except Exception as e:
        print(f"[Session {session_id}] ❌ S3 connection failed: {str(e)}")
        return False


# ============================================================================
# NEW HIERARCHICAL S3 STORAGE FUNCTIONS
# ============================================================================

def sanitize_filename(filename: str) -> str:
    """
    Sanitize filename for S3 storage by replacing invalid characters.

    Args:
        filename: Original filename

    Returns:
        Sanitized filename safe for S3
    """
    # Replace invalid characters with underscores
    invalid_chars = ['<', '>', ':', '"', '|', '?', '*', '/', '\\', '#']
    sanitized = filename
    for char in invalid_chars:
        sanitized = sanitized.replace(char, '_')

    # Remove multiple consecutive underscores
    while '__' in sanitized:
        sanitized = sanitized.replace('__', '_')

    # Remove leading/trailing underscores
    sanitized = sanitized.strip('_')

    return sanitized

def generate_hierarchical_s3_path(
    plant_name: str,
    data_type: str,
    sk_value: str,
    session_id: str = "unknown"
) -> Optional[str]:
    """
    Generate hierarchical S3 path based on database metadata and sk value.

    Args:
        plant_name: Name of the power plant
        data_type: Type of data ('organization', 'plant', 'unit', 'transition')
        sk_value: SK value from the JSON data
        session_id: Session ID for logging

    Returns:
        S3 key path or None if metadata not found

    Example paths:
        - Organization: "India/ORG_IN_657FE5_51516770/ORG_IN_657FE5_51516770.json"
        - Plant: "India/ORG_IN_657FE5_51516770/550e8400-e29b-41d4-a716-************/plant#coal#1.json"
        - Unit: "India/ORG_IN_657FE5_51516770/550e8400-e29b-41d4-a716-************/unit#coal#1#plant#1.json"
    """
    try:
        # Import database manager
        from agent.database_manager import get_database_manager

        db_manager = get_database_manager()
        metadata = db_manager.get_plant_s3_metadata(plant_name)

        if not metadata:
            print(f"[Session {session_id}] ❌ No database metadata found for plant: {plant_name}")
            return None

        # Extract metadata
        country = sanitize_filename(metadata['country'])
        org_uuid = metadata['org_uuid']
        plant_uuid = metadata['plant_uuid']

        # Generate path based on data type
        if data_type == "organization":
            # Organization level: country/org_uuid/org_uuid.json
            s3_path = f"{country}/{org_uuid}/{org_uuid}.json"
        else:
            # Plant/Unit/Transition level: country/org_uuid/plant_uuid/sk_value.json
            sanitized_sk = sanitize_filename(sk_value)
            s3_path = f"{country}/{org_uuid}/{plant_uuid}/{sanitized_sk}.json"

        print(f"[Session {session_id}] 🗂️ Generated S3 path: {s3_path}")
        return s3_path

    except Exception as e:
        print(f"[Session {session_id}] ❌ Error generating hierarchical S3 path: {e}")
        return None

def upload_hierarchical_json_to_s3(
    json_data: Dict[Any, Any],
    plant_name: str,
    data_type: str,
    session_id: str = "unknown"
) -> Optional[str]:
    """
    Upload JSON data to S3 using hierarchical folder structure and sk-based naming.

    Args:
        json_data: Dictionary containing the data to upload
        plant_name: Name of the power plant
        data_type: Type of data ('organization', 'plant', 'unit', 'transition')
        session_id: Session ID for logging

    Returns:
        S3 URL of uploaded file, or None if upload failed
    """
    try:
        # Get sk value from JSON data
        sk_value = json_data.get('sk', '')
        if not sk_value:
            print(f"[Session {session_id}] ❌ No 'sk' field found in JSON data")
            return None

        # Generate hierarchical S3 path
        s3_key = generate_hierarchical_s3_path(plant_name, data_type, sk_value, session_id)
        if not s3_key:
            return None

        # Initialize S3 client
        s3_client = boto3.client(
            's3',
            aws_access_key_id=S3_AWS_ACCESS_KEY,
            aws_secret_access_key=S3_AWS_SECRET_KEY,
            region_name=AWS_REGION
        )

        # Add metadata to JSON
        enhanced_data = {
            **json_data,
            "metadata": {
                "uploaded_at": datetime.utcnow().isoformat() + "Z",
                "session_id": session_id,
                "data_type": data_type,
                "s3_path": s3_key,
                "bucket": S3_BUCKET,
                "storage_version": "hierarchical_v1"
            }
        }

        # Convert to JSON string
        json_string = json.dumps(enhanced_data, indent=2, ensure_ascii=False)

        # Upload to S3
        s3_client.put_object(
            Bucket=S3_BUCKET,
            Key=s3_key,
            Body=json_string,
            ContentType='application/json',
            ContentEncoding='utf-8'
        )

        # Generate S3 URL
        s3_url = f"https://{S3_BUCKET}.s3.amazonaws.com/{s3_key}"

        print(f"[Session {session_id}] ✅ Hierarchical JSON uploaded: {s3_url}")
        return s3_url

    except Exception as e:
        print(f"[Session {session_id}] ❌ Failed to upload hierarchical JSON: {str(e)}")
        return None

def store_hierarchical_organization_data(
    org_data: Dict[Any, Any],
    plant_name: str,
    session_id: str = "unknown"
) -> Optional[str]:
    """
    Store organization-level data using hierarchical S3 structure.

    Args:
        org_data: Organization data dictionary
        plant_name: Original plant name from user query
        session_id: Session ID for tracking

    Returns:
        S3 URL of uploaded file
    """
    print(f"[Session {session_id}] 🏢 Storing hierarchical organization data for: {plant_name}")
    return upload_hierarchical_json_to_s3(org_data, plant_name, "organization", session_id)

def store_hierarchical_plant_data(
    plant_data: Dict[Any, Any],
    plant_name: str,
    session_id: str = "unknown"
) -> Optional[str]:
    """
    Store plant-level data using hierarchical S3 structure.

    Args:
        plant_data: Plant data dictionary
        plant_name: Original plant name from user query
        session_id: Session ID for tracking

    Returns:
        S3 URL of uploaded file
    """
    print(f"[Session {session_id}] 🏭 Storing hierarchical plant data for: {plant_name}")
    return upload_hierarchical_json_to_s3(plant_data, plant_name, "plant", session_id)

def store_hierarchical_unit_data(
    unit_data: Dict[Any, Any],
    plant_name: str,
    session_id: str = "unknown"
) -> Optional[str]:
    """
    Store unit-level data using hierarchical S3 structure.

    Args:
        unit_data: Unit data dictionary
        plant_name: Original plant name from user query
        session_id: Session ID for tracking

    Returns:
        S3 URL of uploaded file
    """
    unit_number = unit_data.get('unit_number', 'unknown')
    print(f"[Session {session_id}] ⚡ Storing hierarchical unit data for Unit {unit_number}: {plant_name}")
    return upload_hierarchical_json_to_s3(unit_data, plant_name, "unit", session_id)

def store_hierarchical_transition_data(
    transition_data: Dict[Any, Any],
    plant_name: str,
    session_id: str = "unknown"
) -> Optional[str]:
    """
    Store transition plan data using hierarchical S3 structure.

    Args:
        transition_data: Transition plan data dictionary
        plant_name: Original plant name from user query
        session_id: Session ID for tracking

    Returns:
        S3 URL of uploaded file
    """
    print(f"[Session {session_id}] 📋 Storing hierarchical transition data for: {plant_name}")
    return upload_hierarchical_json_to_s3(transition_data, plant_name, "transition", session_id)

def get_hierarchical_s3_urls(plant_name: str, session_id: str = "unknown") -> Dict[str, Any]:
    """
    Generate hierarchical S3 URLs for all expected files of a plant (for state tracking).

    Args:
        plant_name: Original plant name from user query
        session_id: Session ID for tracking

    Returns:
        Dictionary with hierarchical URL structure for state management
    """
    try:
        # Import database manager
        from agent.database_manager import get_database_manager

        db_manager = get_database_manager()
        metadata = db_manager.get_plant_s3_metadata(plant_name)

        if not metadata:
            print(f"[Session {session_id}] ❌ No database metadata found for hierarchical URLs: {plant_name}")
            return {}

        # Generate base paths
        country = sanitize_filename(metadata['country'])
        org_uuid = metadata['org_uuid']
        plant_uuid = metadata['plant_uuid']

        base_url = f"https://{S3_BUCKET}.s3.amazonaws.com"
        org_path = f"{country}/{org_uuid}"
        plant_path = f"{org_path}/{plant_uuid}"

        return {
            "plant_name": plant_name,
            "metadata": metadata,
            "base_url": base_url,
            "organization": f"{base_url}/{org_path}/{org_uuid}.json",
            "plant_folder": plant_path,
            "plant": None,  # Will be set when plant data is processed (need sk value)
            "units": {},    # Will be populated as units are processed
            "transition": None  # Will be set when transition data is processed
        }

    except Exception as e:
        print(f"[Session {session_id}] ❌ Error generating hierarchical S3 URLs: {e}")
        return {}

def update_hierarchical_url_with_sk(
    url_structure: Dict[str, Any],
    data_type: str,
    sk_value: str,
    unit_number: str = None
) -> None:
    """
    Update the hierarchical URL structure with actual sk-based URLs.

    Args:
        url_structure: URL structure dictionary from get_hierarchical_s3_urls
        data_type: Type of data ('plant', 'unit', 'transition')
        sk_value: SK value from the JSON data
        unit_number: Unit number (for unit data only)
    """
    if not url_structure or 'plant_folder' not in url_structure:
        return

    base_url = url_structure['base_url']
    plant_folder = url_structure['plant_folder']
    sanitized_sk = sanitize_filename(sk_value)

    if data_type == "plant":
        url_structure["plant"] = f"{base_url}/{plant_folder}/{sanitized_sk}.json"
    elif data_type == "unit" and unit_number:
        url_structure["units"][unit_number] = f"{base_url}/{plant_folder}/{sanitized_sk}.json"
    elif data_type == "transition":
        url_structure["transition"] = f"{base_url}/{plant_folder}/{sanitized_sk}.json"

# For testing/debugging
if __name__ == "__main__":
    # Test sanitization
    test_names = [
        "Jhajjar Power Plant",
        "NTPC Dadri (Stage-II)",
        "Adani Mundra Power Station",
        "Tata Power Plant - Unit 1&2"
    ]
    
    print("Testing plant name sanitization:")
    for name in test_names:
        sanitized = sanitize_plant_name(name)
        print(f"'{name}' → '{sanitized}'")
    
    # Test S3 connection
    print("\nTesting S3 connection:")
    check_s3_connection()